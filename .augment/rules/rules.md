---
type: "always_apply"
---

# Global Rules

- Do not comment out functional code. Always remove or rewrite it correctly.
- All page components must have a default export for lazy loading.
- All index.ts files must re-export the default export from the main file in the folder.
- Convex imports must always follow the agreed convention automatically — no manual corrections required.
- Avoid using `any`. Always use explicit types or interfaces.
- Always add null-safety before accessing potentially null values (e.g., user.id).
- No conditional queries without an explicit, stable return type that TypeScript can infer.
- Remove all unused imports, variables, and placeholder code automatically.
- Always log errors with context. Never close the terminal automatically; wait for user confirmation.
- All code must compile, pass linting, and be production-ready without manual fixes.
