---
type: "always_apply"
---

# Global Rules

- Do not comment out functional code. Always remove or rewrite it correctly.
- All page components must have a default export for lazy loading.
- All index.ts files must re-export the default export from the main file in the folder.
- Convex imports must always follow the agreed convention automatically — no manual corrections required.
- Avoid using `any`. Always use explicit types or interfaces.
- Always add null-safety before accessing potentially null values (e.g., user.id).
- No conditional queries without an explicit, stable return type that TypeScript can infer.
- Remove all unused imports, variables, and placeholder code automatically.
- Always log errors with context. Never close the terminal automatically; wait for user confirmation.
- All code must compile, pass linting, and be production-ready without manual fixes.

- Prefer `unknown` over `any` when a type is uncertain. Require explicit narrowing before use.
- Always include type guards (typeof, instanceof, or custom predicates) when narrowing uncertain values.
- Generated code must compile under strict TypeScript settings (e.g., "strict": true, "strictNullChecks": true, "noImplicitAny": true).
- The assistant must refine its own prompts based on recurring TypeScript errors and re-generate fixes until checks are green.

