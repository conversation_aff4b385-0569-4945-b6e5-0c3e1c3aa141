import { httpAction } from "../_generated/server";
import { internal } from "../_generated/api";
import Stripe from "stripe";

// Initialize Stripe function
function getStripe() {
  return new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: "2025-07-30.basil",
  });
}

// Main webhook handler
export const handleStripeWebhook = httpAction(async (ctx, request) => {
  const stripe = getStripe();
  const body = await request.text();
  const signature = request.headers.get("stripe-signature");

  if (!signature) {
    console.error("Missing Stripe signature");
    return new Response("Missing Stripe signature", { status: 400 });
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    console.error("Webhook signature verification failed:", err);
    return new Response("Webhook signature verification failed", { status: 400 });
  }

  console.log("Processing Stripe webhook event:", event.type, event.id);

  // Idempotency check
  const existingEvent = await ctx.runQuery(internal.webhooks.checkEventProcessed, {
    eventId: event.id
  });

  if (existingEvent) {
    console.log("Event already processed:", event.id);
    return new Response("Event already processed", { status: 200 });
  }

  try {
    // Handle different event types
    switch (event.type) {
      case "customer.created":
        await handleCustomerCreated(ctx, event);
        break;
        
      case "checkout.session.completed":
        await handleCheckoutSessionCompleted(ctx, event);
        break;
        
      case "customer.subscription.created":
        await handleSubscriptionCreated(ctx, event);
        break;
        
      case "customer.subscription.updated":
        await handleSubscriptionUpdated(ctx, event);
        break;
        
      case "customer.subscription.deleted":
        await handleSubscriptionDeleted(ctx, event);
        break;
        
      case "customer.subscription.trial_will_end":
        await handleTrialWillEnd(ctx, event);
        break;
        
      case "invoice.paid":
        await handleInvoicePaid(ctx, event);
        break;
        
      case "invoice.payment_failed":
        await handleInvoicePaymentFailed(ctx, event);
        break;
        
      case "payment_intent.succeeded":
        await handlePaymentIntentSucceeded(ctx, event);
        break;
        
      case "payment_intent.payment_failed":
        await handlePaymentIntentFailed(ctx, event);
        break;
        
      default:
        console.log("Unhandled event type:", event.type);
    }

    // Mark event as processed
    await ctx.runMutation(internal.webhooks.markEventProcessed, {
      eventId: event.id,
      eventType: event.type,
    });

    return new Response("Webhook handled successfully", { status: 200 });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return new Response("Error processing webhook", { status: 500 });
  }
});

// Handle customer.created
async function handleCustomerCreated(ctx: any, event: Stripe.Event) {
  const customer = event.data.object as Stripe.Customer;
  console.log("Customer created:", customer.id);
  // Customer creation is handled in createTrialSubscription
}

// Handle checkout.session.completed
async function handleCheckoutSessionCompleted(ctx: any, event: Stripe.Event) {
  const session = event.data.object as Stripe.Checkout.Session;
  console.log("Checkout session completed:", session.id);
  
  if (session.mode === "subscription" && session.subscription) {
    // Subscription will be handled by subscription.created event
    console.log("Subscription checkout completed for:", session.customer);
  }
}

// Handle customer.subscription.created
async function handleSubscriptionCreated(ctx: any, event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription;
  console.log("Subscription created:", subscription.id);
  
  await ctx.runMutation(internal.subscriptions.updateSubscriptionFromWebhook, {
    stripeSubscriptionId: subscription.id,
    status: subscription.status,
    currentPeriodStart: (subscription as any).current_period_start * 1000,
    currentPeriodEnd: (subscription as any).current_period_end * 1000,
    trialEnd: (subscription as any).trial_end ? (subscription as any).trial_end * 1000 : undefined,
    cancelAt: (subscription as any).cancel_at ? (subscription as any).cancel_at * 1000 : undefined,
    cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
    canceledAt: (subscription as any).canceled_at ? (subscription as any).canceled_at * 1000 : undefined,
  });
}

// Handle customer.subscription.updated
async function handleSubscriptionUpdated(ctx: any, event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription;
  console.log("Subscription updated:", subscription.id);
  
  await ctx.runMutation(internal.subscriptions.updateSubscriptionFromWebhook, {
    stripeSubscriptionId: subscription.id,
    status: subscription.status,
    currentPeriodStart: (subscription as any).current_period_start * 1000,
    currentPeriodEnd: (subscription as any).current_period_end * 1000,
    trialEnd: (subscription as any).trial_end ? (subscription as any).trial_end * 1000 : undefined,
    cancelAt: (subscription as any).cancel_at ? (subscription as any).cancel_at * 1000 : undefined,
    cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
    canceledAt: (subscription as any).canceled_at ? (subscription as any).canceled_at * 1000 : undefined,
  });
}

// Handle customer.subscription.deleted
async function handleSubscriptionDeleted(ctx: any, event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription;
  console.log("Subscription deleted:", subscription.id);
  
  await ctx.runMutation(internal.subscriptions.updateSubscriptionFromWebhook, {
    stripeSubscriptionId: subscription.id,
    status: "canceled",
    currentPeriodStart: (subscription as any).current_period_start * 1000,
    currentPeriodEnd: (subscription as any).current_period_end * 1000,
    canceledAt: Date.now(),
  });
}

// Handle customer.subscription.trial_will_end
async function handleTrialWillEnd(ctx: any, event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription;
  console.log("Trial will end:", subscription.id);
  
  // Send trial ending notification
  await ctx.runMutation(internal.trialManagement.sendTrialEndingNotification, {
    stripeSubscriptionId: subscription.id,
  });
}

// Handle invoice.paid
async function handleInvoicePaid(ctx: any, event: Stripe.Event) {
  const invoice = event.data.object as Stripe.Invoice;
  console.log("Invoice paid:", invoice.id);
  
  if ((invoice as any).subscription) {
    // Update subscription status to active
    await ctx.runMutation(internal.subscriptions.updateSubscriptionFromWebhook, {
      stripeSubscriptionId: (invoice as any).subscription as string,
      status: "active",
      currentPeriodStart: (invoice as any).period_start * 1000,
      currentPeriodEnd: (invoice as any).period_end * 1000,
    });
  }
}

// Handle invoice.payment_failed
async function handleInvoicePaymentFailed(ctx: any, event: Stripe.Event) {
  const invoice = event.data.object as Stripe.Invoice;
  console.log("Invoice payment failed:", invoice.id);
  
  if ((invoice as any).subscription) {
    // Update subscription status to past_due
    await ctx.runMutation(internal.subscriptions.updateSubscriptionFromWebhook, {
      stripeSubscriptionId: (invoice as any).subscription as string,
      status: "past_due",
      currentPeriodStart: (invoice as any).period_start * 1000,
      currentPeriodEnd: (invoice as any).period_end * 1000,
    });
  }
}

// Handle payment_intent.succeeded
async function handlePaymentIntentSucceeded(ctx: any, event: Stripe.Event) {
  const paymentIntent = event.data.object as Stripe.PaymentIntent;
  console.log("Payment intent succeeded:", paymentIntent.id);
}

// Handle payment_intent.payment_failed
async function handlePaymentIntentFailed(ctx: any, event: Stripe.Event) {
  const paymentIntent = event.data.object as Stripe.PaymentIntent;
  console.log("Payment intent failed:", paymentIntent.id);
}
