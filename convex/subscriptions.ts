import { v } from "convex/values";
import { mutation, query, action, internalMutation, internalQuery } from "./_generated/server";
// import { internal } from "./_generated/api"; // Unused due to disabled functionality
import Stripe from "stripe";

// Initialize Stripe function
function getStripe() {
  const secretKey = process.env.STRIPE_SECRET_KEY;
  if (!secretKey || secretKey === 'sk_test_your_secret_key_here') {
    throw new Error("Stripe secret key not configured");
  }
  return new Stripe(secretKey, {
    apiVersion: "2025-07-30.basil",
  });
}

// Check if we're in development mode without Stripe
function isStripeConfigured() {
  const secretKey = process.env.STRIPE_SECRET_KEY;
  return secretKey && secretKey !== 'sk_test_your_secret_key_here';
}

// Internal mutation to create trial subscription in database
export const createTrialSubscriptionInternal = internalMutation({
  args: {
    userId: v.string(),
    customerId: v.string(),
    selectedPlan: v.optional(v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise"))),
    selectedBilling: v.optional(v.union(v.literal("month"), v.literal("year"))),
    trialEnd: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Create subscription record with selected plan
    const subscriptionId = await ctx.db.insert("subscriptions", {
      userId: args.userId,
      stripeCustomerId: args.customerId,
      planLevel: args.selectedPlan || "basic",
      billingInterval: args.selectedBilling || "month",
      status: "trialing",
      trialStart: now,
      trialEnd: args.trialEnd,
      currentPeriodStart: now,
      currentPeriodEnd: args.trialEnd,
      createdAt: now,
      updatedAt: now,
      seats: 1,
    });

    // Update user record
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (existingUser) {
      await ctx.db.patch(existingUser._id, {
        trialEndsAt: args.trialEnd,
        trialUsedAt: now,
        hasCompletedTrial: false,
        updatedAt: now,
      });
    }

    return { subscriptionId, customerId: args.customerId };
  },
});

// Create trial subscription with Stripe customer
export const createTrialSubscription: any = action({
  args: {
    userId: v.string(),
    email: v.string(),
    name: v.string(),
    companyName: v.optional(v.string()),
    orgNumber: v.optional(v.string()),
    selectedPlan: v.optional(v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise"))),
    selectedBilling: v.optional(v.union(v.literal("month"), v.literal("year"))),
  },
  handler: async (_ctx, _args): Promise<any> => {
    // Check if user already had a trial
    // TODO: Re-enable when type instantiation issues are resolved
    // const existingUser = await ctx.runQuery(internal.subscriptions.getUserByClerkId as any, {
    //   clerkUserId: args.userId
    // });

    // if (existingUser?.hasCompletedTrial) {
    //   throw new Error("Trial already used");
    // }

    // Check for existing subscription
    // TODO: Re-enable when type instantiation issues are resolved
    // const existingSubscription = await ctx.runQuery(internal.subscriptions.getSubscriptionByUserInternal, {
    //   userId: args.userId
    // });

    // if (existingSubscription) {
    //   throw new Error("User already has a subscription");
    // }

    // const now = Date.now(); // Unused due to disabled functionality
    // const trialEnd = now + (7 * 24 * 60 * 60 * 1000); // 7 days from now - unused due to disabled functionality

    // let customerId = "dev_customer_" + args.userId; // Default for development - unused due to disabled functionality

    // Only create Stripe customer if Stripe is configured
    if (isStripeConfigured()) {
      try {
        // const stripe = getStripe(); // Unused due to disabled functionality
        // const customer = await stripe.customers.create({ // Unused due to disabled functionality
        //   email: args.email,
        //   name: args.name,
        //   metadata: {
        //     userId: args.userId,
        //     companyName: args.companyName || "",
        //     orgNumber: args.orgNumber || ""
        //   },
        //   tax: { validate_location: "immediately" }
        // });
        // customerId = customer.id; // Unused due to disabled functionality
      } catch (error) {
        console.error("Failed to create Stripe customer, using development mode:", error);
        // Continue with development customer ID
      }
    }

    // Create subscription record using internal mutation
    // TODO: Re-enable when type instantiation issues are resolved
    // const result: any = await ctx.runMutation(internal.subscriptions.createTrialSubscriptionInternal, {
    //   userId: args.userId,
    //   customerId,
    //   selectedPlan: args.selectedPlan,
    //   selectedBilling: args.selectedBilling,
    //   trialEnd,
    // });

    // return result;
    return { success: false, error: "Temporarily disabled due to type instantiation issues" };
  },
});

// Get user's subscription (supports team members accessing company subscription)
export const getUserSubscription = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // First try to get user's own subscription
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // If no personal subscription, check if user is part of a team with a subscription
    if (!subscription) {
      // Get the user to check their company and role
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (user && user.contractorCompanyId) {
        // For non-administrators (utfoerende, prosjektleder), look up company administrator's subscription
        if (user.role === "utfoerende" || user.role === "prosjektleder") {
          const administrator = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .first();

          if (administrator) {
            subscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", administrator.clerkUserId))
              .first();
          }
        }
        // For newly promoted administrators, look for any existing subscription in the company
        else if (user.role === "administrator") {
          // First check if there's already an administrator with a subscription
          const existingAdminWithSubscription = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .collect();

          // Look for any administrator in the company who has a subscription
          for (const admin of existingAdminWithSubscription) {
            const adminSubscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", admin.clerkUserId))
              .first();

            if (adminSubscription) {
              subscription = adminSubscription;
              break;
            }
          }
        }
      }
    }

    return subscription;
  },
});

// Internal query to get user subscription (for use in actions)
export const getUserSubscriptionInternal = internalQuery({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // First try to get user's own subscription
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // If no personal subscription, check if user is part of a team with a subscription
    if (!subscription) {
      // Get the user to check their company and role
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (user && user.contractorCompanyId) {
        // For non-administrators (utfoerende, prosjektleder), look up company administrator's subscription
        if (user.role === "utfoerende" || user.role === "prosjektleder") {
          const administrator = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .first();

          if (administrator) {
            subscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", administrator.clerkUserId))
              .first();
          }
        }
        // For administrators, look for any existing subscription in the company
        else if (user.role === "administrator") {
          const existingAdminWithSubscription = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .collect();

          // Look for any administrator in the company who has a subscription
          for (const admin of existingAdminWithSubscription) {
            const adminSubscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", admin.clerkUserId))
              .first();

            if (adminSubscription) {
              subscription = adminSubscription;
              break;
            }
          }
        }
      }
    }

    return subscription;
  },
});

// Internal query to get user by Clerk ID
export const getUserByClerkId = internalQuery({
  args: { clerkUserId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();
  },
});

// Internal mutation to create subscription record
export const createSubscriptionRecord = internalMutation({
  args: {
    userId: v.string(),
    stripeCustomerId: v.string(),
    planLevel: v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise")),
    billingInterval: v.union(v.literal("month"), v.literal("year")),
    trialStart: v.number(),
    trialEnd: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    return await ctx.db.insert("subscriptions", {
      userId: args.userId,
      stripeCustomerId: args.stripeCustomerId,
      status: "trialing",
      planLevel: args.planLevel,
      billingInterval: args.billingInterval,
      currentPeriodStart: now,
      currentPeriodEnd: args.trialEnd,
      trialStart: args.trialStart,
      trialEnd: args.trialEnd,
      seats: 1, // Start with 1 seat (the user themselves)
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Internal mutation to update user trial status
export const updateUserTrialStatus = internalMutation({
  args: {
    userId: v.string(),
    trialEndsAt: v.number(),
    trialUsedAt: v.number(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (user) {
      await ctx.db.patch(user._id, {
        subscriptionStatus: "trialing",
        trialEndsAt: args.trialEndsAt,
        trialUsedAt: args.trialUsedAt,
        updatedAt: Date.now(),
      });
    }
  },
});

// Internal mutation to update subscription portal URL
export const updateSubscriptionPortalUrl = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    portalUrl: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.subscriptionId, {
      portalUrl: args.portalUrl,
      updatedAt: Date.now(),
    });
  },
});

// Test mutation
export const testMutation = mutation({
  args: {
    message: v.string(),
  },
  handler: async (_ctx, args) => {
    console.log('🔥 testMutation called with:', args);
    return { success: true, message: args.message };
  },
});

// Create Customer Portal session
export const createPortalSession = mutation({
  args: {
    userId: v.string(),
    returnUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log('🔥 createPortalSession called with:', args);
    console.log('createPortalSession called with args:', args);

    // Use the same logic as getUserSubscription to find the subscription
    // First try to get user's own subscription
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // If no personal subscription, check if user is part of a team with a subscription
    if (!subscription) {
      console.log('No direct subscription found, checking team subscription...');

      // Get the user to check their company and role
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (user && user.contractorCompanyId) {
        console.log('User has company ID:', user.contractorCompanyId, 'Role:', user.role);

        // For non-administrators (utfoerende, prosjektleder), look up company administrator's subscription
        if (user.role === "utfoerende" || user.role === "prosjektleder") {
          const administrator = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .first();

          if (administrator) {
            console.log('Found administrator:', administrator.clerkUserId);
            subscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", administrator.clerkUserId))
              .first();
          }
        }
        // For administrators, look for any existing subscription in the company
        else if (user.role === "administrator") {
          const existingAdminWithSubscription = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .collect();

          // Look for any administrator in the company who has a subscription
          for (const admin of existingAdminWithSubscription) {
            const adminSubscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", admin.clerkUserId))
              .first();

            if (adminSubscription) {
              subscription = adminSubscription;
              break;
            }
          }
        }
      }
    }

    console.log('Final subscription found:', subscription ? 'Yes' : 'No');

    if (!subscription) {
      console.error('No subscription found for user:', args.userId);
      throw new Error("No subscription found for user. Please start a trial first.");
    }

    // In development mode without Stripe, return a mock URL
    if (!isStripeConfigured()) {
      console.log('Stripe not configured, using mock portal');
      const mockUrl = args.returnUrl || `${process.env.CONVEX_SITE_URL || 'http://localhost:5173'}/dashboard?mock_portal=true`;
      console.log('Mock URL:', mockUrl);

      // Update subscription with mock portal URL
      await ctx.db.patch(subscription._id, {
        portalUrl: mockUrl,
        updatedAt: Date.now(),
      });

      return { url: mockUrl };
    }

    // Use real Stripe portal in production
    console.log('Stripe configured, creating real portal session');
    console.log('Customer ID:', subscription.stripeCustomerId);

    try {
      const stripe = getStripe();
      const session = await stripe.billingPortal.sessions.create({
        customer: subscription.stripeCustomerId,
        return_url: args.returnUrl || `${process.env.CONVEX_SITE_URL}/dashboard`,
      });

      console.log('Stripe portal session created:', session.url);

      // Update subscription with portal URL
      await ctx.db.patch(subscription._id, {
        portalUrl: session.url,
        updatedAt: Date.now(),
      });

      return { url: session.url };
    } catch (stripeError) {
      console.error('Stripe portal session creation failed:', stripeError);
      const errorMessage = stripeError instanceof Error ? stripeError.message : 'Unknown error';
      throw new Error(`Failed to create Stripe portal session: ${errorMessage}`);
    }
  },
});

// Create Stripe Checkout session
export const createCheckoutSession = mutation({
  args: {
    userId: v.string(),
    priceId: v.string(),
    successUrl: v.optional(v.string()),
    cancelUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const stripe = getStripe();

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("No subscription found");
    }

    const session = await stripe.checkout.sessions.create({
      customer: subscription.stripeCustomerId,
      payment_method_types: ["card"],
      mode: "subscription",
      line_items: [
        {
          price: args.priceId,
          quantity: 1,
        },
      ],
      success_url: args.successUrl || `${process.env.CONVEX_SITE_URL}/dashboard?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: args.cancelUrl || `${process.env.CONVEX_SITE_URL}/dashboard`,
      billing_address_collection: "required",
      tax_id_collection: { enabled: true },
      allow_promotion_codes: true,
      metadata: {
        userId: args.userId,
      },
    });

    return { url: session.url, sessionId: session.id };
  },
});

// Internal function to update subscription from webhook
export const updateSubscriptionFromWebhook = internalMutation({
  args: {
    stripeSubscriptionId: v.string(),
    status: v.string(),
    currentPeriodStart: v.number(),
    currentPeriodEnd: v.number(),
    cancelAt: v.optional(v.number()),
    cancelAtPeriodEnd: v.optional(v.boolean()),
    canceledAt: v.optional(v.number()),
    trialEnd: v.optional(v.number()),
    planLevel: v.optional(v.string()),
    billingInterval: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_subscription", (q) => q.eq("stripeSubscriptionId", args.stripeSubscriptionId))
      .first();

    if (!subscription) {
      console.error("Subscription not found for Stripe ID:", args.stripeSubscriptionId);
      return;
    }

    // Update subscription
    await ctx.db.patch(subscription._id, {
      status: args.status as any,
      currentPeriodStart: args.currentPeriodStart,
      currentPeriodEnd: args.currentPeriodEnd,
      cancelAt: args.cancelAt,
      cancelAtPeriodEnd: args.cancelAtPeriodEnd,
      canceledAt: args.canceledAt,
      trialEnd: args.trialEnd,
      planLevel: args.planLevel as any,
      billingInterval: args.billingInterval as any,
      updatedAt: Date.now(),
    });

    // Update user subscription status
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", subscription.userId))
      .first();

    if (user) {
      await ctx.db.patch(user._id, {
        subscriptionStatus: args.status as any,
        trialEndsAt: args.trialEnd,
        updatedAt: Date.now(),
      });
    }
  },
});

// Update plan during trial period
export const updateTrialPlan = mutation({
  args: {
    userId: v.string(),
    newPlanLevel: v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise")),
    newBillingInterval: v.union(v.literal("month"), v.literal("year")),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("No subscription found");
    }

    // Only allow plan changes during trial period
    if (subscription.status !== "trialing") {
      throw new Error("Plan changes are only allowed during trial period");
    }

    const now = Date.now();

    // Update subscription with new plan details while preserving trial dates
    await ctx.db.patch(subscription._id, {
      planLevel: args.newPlanLevel,
      billingInterval: args.newBillingInterval,
      updatedAt: now,
      // Preserve trial dates - do NOT change trialStart or trialEnd
    });

    // Log the plan change
    await ctx.db.insert("subscriptionHistory", {
      subscriptionId: subscription._id,
      userId: args.userId,
      action: "plan_changed_during_trial",
      oldPlanLevel: subscription.planLevel,
      newPlanLevel: args.newPlanLevel,
      oldBillingInterval: subscription.billingInterval,
      newBillingInterval: args.newBillingInterval,
      timestamp: now,
    });

    return {
      success: true,
      newPlan: args.newPlanLevel,
      newBilling: args.newBillingInterval,
      trialEnd: subscription.trialEnd, // Return trial end to confirm it's preserved
    };
  },
});

// Get subscription status for access control (supports team members)
export const getSubscriptionStatus = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // First try to get user's own subscription
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // If no personal subscription, check if user is part of a team with a subscription
    if (!subscription) {
      // Get the user to check their company and role
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (user && user.contractorCompanyId) {
        // For non-administrators (utfoerende, prosjektleder), look up company administrator's subscription
        if (user.role === "utfoerende" || user.role === "prosjektleder") {
          const administrator = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .first();

          if (administrator) {
            subscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", administrator.clerkUserId))
              .first();
          }
        }
        // For newly promoted administrators, look for any existing subscription in the company
        else if (user.role === "administrator") {
          // First check if there's already an administrator with a subscription
          const existingAdminWithSubscription = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .collect();

          // Look for any administrator in the company who has a subscription
          for (const admin of existingAdminWithSubscription) {
            const adminSubscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", admin.clerkUserId))
              .first();

            if (adminSubscription) {
              subscription = adminSubscription;
              break;
            }
          }
        }
      }
    }

    if (!subscription) {
      return { hasSubscription: false };
    }

    const now = Date.now();
    const isTrialExpired = subscription.status === "trialing" && subscription.trialEnd && subscription.trialEnd < now;
    const hasActiveSubscription = subscription.status === "active";
    const isInTrial = subscription.status === "trialing" && !isTrialExpired;
    const isInGracePeriod = ["past_due", "incomplete", "unpaid"].includes(subscription.status);

    return {
      hasSubscription: true,
      subscription,
      hasActiveSubscription,
      isInTrial,
      isTrialExpired,
      isInGracePeriod,
      canCreateProjects: hasActiveSubscription || isInTrial,
      canAccessProjects: hasActiveSubscription || isInTrial || isInGracePeriod,
      hasFullAccess: hasActiveSubscription || isInTrial,
      isReadOnly: isInGracePeriod || isTrialExpired,
      needsUpgrade: isTrialExpired || subscription.status === "past_due",
    };
  },
});

// Internal queries for actions (using existing getUserByClerkId)
export const getSubscriptionByUserInternal = internalQuery({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();
  },
});
