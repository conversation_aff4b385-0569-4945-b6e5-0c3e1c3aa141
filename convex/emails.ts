import { action } from './_generated/server';
import { v } from 'convex/values';
import { Resend } from 'resend';
import { generateTeamInvitationEmail, generateJobbLoggInvitationEmail, generateCustomerNotificationEmail } from './emailTemplates';
import { internal } from './_generated/api';

// Utility function to sanitize tag values for Resend API
// Tags should only contain ASCII letters, numbers, underscores, or dashes
const sanitizeTagValue = (value: string): string => {
  return value
    .replace(/[^a-zA-Z0-9_-]/g, '_') // Replace invalid characters with underscores
    .substring(0, 256); // Limit length (Resend has tag value limits)
};

// Helper function to format Norwegian date
const formatNorwegianDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleDateString('nb-NO', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
};

// Helper function to format urgency in Norwegian
const formatUrgency = (urgency: string): string => {
  switch (urgency) {
    case 'high': return 'Høy prioritet';
    case 'medium': return 'Middels prioritet';
    case 'low': return 'Lav prioritet';
    default: return 'Ikke oppgitt';
  }
};

// Send team invitation email
export const sendTeamInvitationEmail = action({
  args: {
    to: v.string(),
    invitedByName: v.string(),
    inviterEmail: v.string(),
    companyName: v.string(),
    invitationLink: v.string(),
    expiresAt: v.number(),
    role: v.union(v.literal('administrator'), v.literal('utfoerende')),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending team invitation email to:', args.to);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL'; // Your actual API key
      const resend = new Resend(apiKey);

      // Generate email content using template
      const emailContent = generateTeamInvitationEmail({
        invitedByName: args.invitedByName,
        inviterEmail: args.inviterEmail,
        companyName: args.companyName,
        invitationLink: args.invitationLink,
        expiresAt: args.expiresAt,
        role: args.role,
      });

      // Send email via Resend
      const result = await resend.emails.send({
        from: '<EMAIL>', // Use Resend's default verified domain for testing
        to: args.to,
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        // Temporarily remove tags to fix validation error
        // tags: [
        //   { name: 'type', value: 'team_invitation' },
        //   { name: 'role', value: args.role },
        // ],
      });

      console.log('✅ Email sent successfully:', result);

      console.log('✅ Email sent successfully:', result);

      return {
        success: true,
        emailId: result.data?.id,
        message: 'Invitasjons-email sendt',
      };

    } catch (error) {
      console.error('❌ Failed to send email:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil ved sending av e-post',
        message: 'Kunne ikke sende invitasjons-email',
      };
    }
  },
});

// Send JobbLogg team invitation email
export const sendJobbLoggInvitationEmail = action({
  args: {
    to: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    invitedByName: v.string(),
    inviterEmail: v.string(),
    companyName: v.string(),
    invitationLink: v.string(),
    expiresAt: v.number(),
    role: v.union(v.literal('administrator'), v.literal('utfoerende')),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending JobbLogg invitation email to:', args.to);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL'; // Your actual API key

      if (!apiKey || apiKey.length < 10) {
        throw new Error('Resend API key not configured');
      }

      const resend = new Resend(apiKey);
      console.log('🔑 Resend initialized with API key:', apiKey.substring(0, 10) + '...');

      // Generate email content using template
      const emailContent = generateJobbLoggInvitationEmail({
        firstName: args.firstName,
        lastName: args.lastName,
        invitedByName: args.invitedByName,
        inviterEmail: args.inviterEmail,
        companyName: args.companyName,
        invitationLink: args.invitationLink,
        expiresAt: args.expiresAt,
        role: args.role,
      });

      // In development/testing mode, Resend can only send to verified email addresses
      // For testing purposes, we'll send to the verified email but include original recipient info
      const isDevelopment = true; // Set to false when you have a verified domain
      const verifiedEmail = '<EMAIL>'; // Your verified email address

      const actualRecipient = args.to;
      const emailRecipient = isDevelopment ? verifiedEmail : actualRecipient;

      // Modify subject and content for development mode
      let modifiedSubject = emailContent.subject;
      let modifiedHtml = emailContent.html;
      let modifiedText = emailContent.text;

      if (isDevelopment && actualRecipient !== verifiedEmail) {
        modifiedSubject = `[DEV - For: ${actualRecipient}] ${emailContent.subject}`;

        const devNotice = `
          <div style="background: #fef3c7; border: 1px solid #fbbf24; padding: 15px; margin: 20px 0; border-radius: 8px;">
            <strong>🧪 DEVELOPMENT MODE</strong><br>
            This email was intended for: <strong>${actualRecipient}</strong><br>
            But sent to your verified email for testing purposes.
          </div>
        `;

        modifiedHtml = modifiedHtml.replace('<div class="content">', `<div class="content">${devNotice}`);
        modifiedText = `[DEVELOPMENT MODE - Intended for: ${actualRecipient}]\n\n${modifiedText}`;
      }

      // Send email via Resend
      const emailPayload = {
        from: '<EMAIL>', // Use Resend's default verified domain for testing
        to: emailRecipient,
        subject: modifiedSubject,
        html: modifiedHtml,
        text: modifiedText,
        // Temporarily remove tags to fix validation error
        // tags: [
        //   { name: 'type', value: 'magic_link_invitation' },
        //   { name: 'role', value: args.role },
        //   { name: 'mode', value: isDevelopment ? 'development' : 'production' },
        //   { name: 'recipient_type', value: isDevelopment ? 'dev_redirect' : 'direct' },
        // ],
      };

      console.log('📤 Sending email with payload:', {
        from: emailPayload.from,
        to: emailPayload.to,
        originalRecipient: actualRecipient,
        subject: emailPayload.subject,
        role: args.role,
        isDevelopment,
        htmlLength: emailPayload.html.length,
        textLength: emailPayload.text.length
      });

      const result = await resend.emails.send(emailPayload);

      console.log('✅ Magic link email sent successfully:', {
        success: !!result.data,
        emailId: result.data?.id,
        error: result.error
      });

      const successMessage = isDevelopment && actualRecipient !== verifiedEmail
        ? `Magic link invitasjon sendt til ${verifiedEmail} (DEV MODE - ment for ${args.firstName} ${args.lastName} - ${actualRecipient})`
        : `Magic link invitasjon sendt til ${args.firstName} ${args.lastName} (${args.to})`;

      return {
        success: true,
        emailId: result.data?.id,
        message: successMessage,
        developmentMode: isDevelopment,
        actualRecipient: emailRecipient,
        intendedRecipient: actualRecipient,
      };

    } catch (error) {
      console.error('❌ Failed to send magic link email:', {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        to: args.to,
        role: args.role,
        companyName: args.companyName
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil ved sending av e-post',
        message: 'Kunne ikke sende invitasjons-email',
      };
    }
  },
});

// Test Resend API connection
export const testResendConnection = action({
  args: {
    testEmail: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      console.log('🧪 Testing Resend API connection...');

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL';

      if (!apiKey || apiKey.length < 10) {
        throw new Error('Resend API key not configured');
      }

      const resend = new Resend(apiKey);

      // Send simple test email
      const result = await resend.emails.send({
        from: '<EMAIL>',
        to: args.testEmail,
        subject: 'Test Email from JobbLogg',
        html: '<h1>Test Email</h1><p>This is a test email to verify Resend integration is working.</p>',
        text: 'Test Email - This is a test email to verify Resend integration is working.',
      });

      console.log('✅ Test email result:', result);

      return {
        success: !!result.data,
        emailId: result.data?.id,
        error: result.error,
        message: result.data ? 'Test email sent successfully' : 'Failed to send test email'
      };

    } catch (error) {
      console.error('❌ Resend test failed:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Resend API test failed'
      };
    }
  },
});

// Send general notification email (for future use)
export const sendNotificationEmail = action({
  args: {
    to: v.string(),
    subject: v.string(),
    html: v.string(),
    text: v.optional(v.string()),
    tags: v.optional(v.array(v.object({
      name: v.string(),
      value: v.string(),
    }))),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending notification email to:', args.to);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL'; // Your actual API key
      const resend = new Resend(apiKey);

      const result = await resend.emails.send({
        from: '<EMAIL>', // Use Resend's default verified domain for testing
        to: args.to,
        subject: args.subject,
        html: args.html,
        text: args.text || args.html.replace(/<[^>]*>/g, ''), // Strip HTML for text version
        tags: (args.tags || []).map(tag => ({
          name: tag.name,
          value: sanitizeTagValue(tag.value)
        })),
      });

      console.log('✅ Notification email sent:', result);

      console.log('✅ Notification email sent:', result);

      return {
        success: true,
        emailId: result.data?.id,
        message: 'E-post sendt',
      };

    } catch (error) {
      console.error('❌ Failed to send notification email:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil ved sending av e-post',
        message: 'Kunne ikke sende e-post',
      };
    }
  },
});

// Test email function for development
export const sendTestEmail = action({
  args: {
    to: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending test email to:', args.to);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL'; // Your actual API key
      console.log('📧 Using API key:', apiKey.substring(0, 10) + '...');
      const resend = new Resend(apiKey);
      console.log('📧 Resend client initialized');

      console.log('📧 About to send email...');
      const emailData = {
        from: '<EMAIL>', // Use Resend's default verified domain for testing
        to: args.to,
        subject: 'Test email fra JobbLogg',
        html: `
          <h1>Test Email</h1>
          <p>Dette er en test-email fra JobbLogg for å verifisere at email-sending fungerer.</p>
          <p>Sendt: ${new Date().toLocaleString('nb-NO')}</p>
        `,
        text: `Test Email\n\nDette er en test-email fra JobbLogg for å verifisere at email-sending fungerer.\n\nSendt: ${new Date().toLocaleString('nb-NO')}`,
      };
      console.log('📧 Email data:', JSON.stringify(emailData, null, 2));

      const result = await resend.emails.send(emailData);

      console.log('✅ Email sent successfully:', result);
      console.log('📧 Result data:', JSON.stringify(result, null, 2));

      return {
        success: true,
        emailId: result.data?.id,
        message: 'Test-email sendt',
        debug: result,
      };

    } catch (error) {
      console.error('❌ Test email failed:', error);
      console.error('❌ Error details:', JSON.stringify(error, null, 2));

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil',
        message: 'Test-email feilet',
        debug: error,
      };
    }
  },
});

// Send customer notification email when a new project is created
export const sendCustomerNotificationEmail = action({
  args: {
    customerEmail: v.string(),
    customerName: v.string(),
    customerType: v.union(v.literal('privat'), v.literal('bedrift')),
    contactPerson: v.optional(v.string()),
    projectName: v.string(),
    projectDescription: v.string(),
    contractorCompanyName: v.string(),
    contractorContactPerson: v.string(),
    contractorPhone: v.optional(v.string()),
    contractorEmail: v.optional(v.string()),
    sharedProjectUrl: v.string(),
    // Tracking parameters
    userId: v.string(),
    projectId: v.optional(v.id("projects")),
    customerId: v.optional(v.id("customers")),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending customer notification email to:', args.customerEmail);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL';
      const resend = new Resend(apiKey);

      // Generate a unique tracking ID for this email (using nanoid for actions)
      const { nanoid } = await import('nanoid');
      const trackingId = nanoid();

      // Generate email content using template with tracking
      const emailContent = generateCustomerNotificationEmail({
        customerName: args.customerName,
        customerType: args.customerType,
        contactPerson: args.contactPerson,
        projectName: args.projectName,
        projectDescription: args.projectDescription,
        contractorCompanyName: args.contractorCompanyName,
        contractorContactPerson: args.contractorContactPerson,
        contractorPhone: args.contractorPhone,
        contractorEmail: args.contractorEmail,
        sharedProjectUrl: args.sharedProjectUrl,
        emailId: trackingId,
      });

      // Prepare sanitized tags
      const sanitizedContractorName = sanitizeTagValue(args.contractorCompanyName);
      console.log('🏷️ Tag sanitization:', {
        original: args.contractorCompanyName,
        sanitized: sanitizedContractorName
      });

      const emailTags = [
        { name: 'type', value: 'customer_notification' },
        { name: 'customer_type', value: sanitizeTagValue(args.customerType) },
        { name: 'contractor', value: sanitizedContractorName },
      ];

      console.log('📧 Email tags to send:', emailTags);

      // Send email via Resend with improved deliverability headers
      const result = await resend.emails.send({
        from: `${args.contractorCompanyName} <<EMAIL>>`, // Use contractor name as sender
        to: args.customerEmail,
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: emailTags,
        headers: {
          'X-Entity-Ref-ID': trackingId, // Internal reference for tracking
          'List-Unsubscribe': '<mailto:<EMAIL>>',
          'X-Mailer': 'JobbLogg Project Management System',
        },
      });

      console.log('✅ Customer notification email sent successfully:', result);

      // Create email tracking record
      try {
        const resendEmailId = result.data?.id;
        console.log(`📧 Creating tracking record with Resend ID: ${resendEmailId}, Tracking ID: ${trackingId}`);

        await ctx.runMutation(internal.emailTracking.createEmailTrackingRecord, {
          emailId: resendEmailId,
          trackingId: trackingId,
          emailType: "customer_notification",
          recipientEmail: args.customerEmail,
          recipientName: args.customerType === 'bedrift' && args.contactPerson
            ? args.contactPerson
            : args.customerName,
          subject: emailContent.subject,
          templateUsed: "generateCustomerNotificationEmail",
          status: result.data?.id ? "sent" : "failed",
          userId: args.userId,
          projectId: args.projectId,
          customerId: args.customerId,
          errorMessage: result.error ? String(result.error) : undefined,
        });
        console.log('📊 Email tracking record created');
      } catch (trackingError) {
        console.error('⚠️ Failed to create email tracking record:', trackingError);
        // Don't fail the email sending if tracking fails
      }

      return {
        success: true,
        emailId: result.data?.id,
        message: 'Kunde-varsling sendt',
      };
    } catch (error) {
      console.error('❌ Failed to send customer notification email:', error);

      // Create tracking record for failed email
      try {
        const emailContent = generateCustomerNotificationEmail({
          customerName: args.customerName,
          customerType: args.customerType,
          contactPerson: args.contactPerson,
          projectName: args.projectName,
          projectDescription: args.projectDescription,
          contractorCompanyName: args.contractorCompanyName,
          contractorContactPerson: args.contractorContactPerson,
          contractorPhone: args.contractorPhone,
          contractorEmail: args.contractorEmail,
          sharedProjectUrl: args.sharedProjectUrl,
        });

        await ctx.runMutation(internal.emailTracking.createEmailTrackingRecord, {
          emailId: undefined,
          emailType: "customer_notification",
          recipientEmail: args.customerEmail,
          recipientName: args.customerType === 'bedrift' && args.contactPerson
            ? args.contactPerson
            : args.customerName,
          subject: emailContent.subject,
          templateUsed: "generateCustomerNotificationEmail",
          status: "failed",
          userId: args.userId,
          projectId: args.projectId,
          customerId: args.customerId,
          errorMessage: error instanceof Error ? error.message : 'Ukjent feil',
        });
        console.log('📊 Failed email tracking record created');
      } catch (trackingError) {
        console.error('⚠️ Failed to create failed email tracking record:', trackingError);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil',
        message: 'Kunde-varsling feilet',
      };
    }
  },
});

// Send subcontractor project invitation email
export const sendInvitationEmail = action({
  args: {
    invitationId: v.id("projectAssignments"),
    subcontractorEmail: v.string(),
    subcontractorName: v.string(),
    projectName: v.string(),
    projectAddress: v.string(),
    mainContractorName: v.string(),
    inviterName: v.string(),
    inviterEmail: v.optional(v.string()),
    inviterPhone: v.optional(v.string()),
    inviterRole: v.optional(v.string()),
    specialization: v.string(),
    projectDescription: v.string(),
    estimatedDuration: v.string(),
    invitationMessage: v.optional(v.string()),
    urgency: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    expiresAt: v.number(),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending subcontractor invitation email to:', args.subcontractorEmail);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL';
      const resend = new Resend(apiKey);

      // Create invitation link
      const invitationLink = `${process.env.SITE_URL || 'http://localhost:5173'}/invitations/${args.invitationId}`;

      // Generate email content
      const subject = `Prosjektinvitasjon: ${args.projectName} - ${args.mainContractorName}`;

      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject}</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
            .header { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; padding: 32px 24px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
            .content { padding: 32px 24px; }
            .project-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .project-title { font-size: 20px; font-weight: 600; color: #1e293b; margin: 0 0 8px 0; }
            .project-meta { color: #64748b; font-size: 14px; margin: 4px 0; }
            .urgency-high { color: #dc2626; font-weight: 600; }
            .urgency-medium { color: #d97706; font-weight: 600; }
            .urgency-low { color: #059669; font-weight: 600; }
            .message-box { background: #eff6ff; border-left: 4px solid #2563eb; padding: 16px; margin: 20px 0; border-radius: 0 8px 8px 0; }
            .cta-container { text-align: center; margin: 32px 0; }
            .cta-button { display: inline-block; background: #2563eb; color: white; text-decoration: none; padding: 14px 28px; border-radius: 8px; font-weight: 600; font-size: 16px; }
            .cta-button:hover { background: #1d4ed8; }
            .footer { background: #f8fafc; padding: 24px; text-align: center; color: #64748b; font-size: 14px; border-top: 1px solid #e2e8f0; }
            .expires { color: #dc2626; font-weight: 600; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🔧 Prosjektinvitasjon</h1>
              <p style="margin: 8px 0 0 0; opacity: 0.9;">Du har blitt invitert som underleverandør</p>
            </div>

            <div class="content">
              <p>Hei <strong>${args.subcontractorName}</strong>,</p>

              <p>Du har blitt invitert til å delta som underleverandør på følgende prosjekt:</p>

              <div class="project-card">
                <div class="project-title">${args.projectName}</div>
                <div class="project-meta">📍 <strong>Adresse:</strong> ${args.projectAddress}</div>
                <div class="project-meta">🏢 <strong>Hovedentreprenør:</strong> ${args.mainContractorName}</div>
                <div class="project-meta">👤 <strong>Invitert av:</strong> ${args.inviterName}</div>
                <div class="project-meta">🔧 <strong>Din rolle:</strong> ${args.specialization}</div>
                <div class="project-meta">⏱️ <strong>Estimert varighet:</strong> ${args.estimatedDuration}</div>
                <div class="project-meta">🚨 <strong>Prioritet:</strong> <span class="urgency-${args.urgency}">${formatUrgency(args.urgency)}</span></div>
              </div>

              ${args.projectDescription ? `
                <div>
                  <h3 style="color: #1e293b; margin: 24px 0 12px 0;">Prosjektbeskrivelse:</h3>
                  <p style="background: #f8fafc; padding: 16px; border-radius: 8px; margin: 0;">${args.projectDescription}</p>
                </div>
              ` : ''}

              ${args.invitationMessage ? `
                <div class="message-box">
                  <h4 style="margin: 0 0 8px 0; color: #2563eb;">Melding fra ${args.inviterName}:</h4>
                  <p style="margin: 0;">${args.invitationMessage}</p>
                </div>
              ` : ''}

              <div class="cta-container">
                <a href="${invitationLink}" class="cta-button">Se invitasjon og svar</a>
              </div>

              <p style="text-align: center; margin: 24px 0;">
                <span class="expires">Invitasjonen utløper ${formatNorwegianDate(args.expiresAt)}</span>
              </p>

              <p style="color: #64748b; font-size: 14px;">
                Hvis du har spørsmål om prosjektet, kan du svare på denne invitasjonen eller kontakte ${args.inviterName} direkte.
              </p>
            </div>

            <div class="footer">
              <div style="margin-bottom: 20px;">
                <p style="margin: 0 0 8px 0;">Med vennlig hilsen,</p>
                <p style="margin: 0; font-weight: 600; font-size: 16px; color: #1e293b;">${args.inviterName}</p>
                ${args.inviterRole ? `<p style="margin: 2px 0 0 0; color: #64748b; font-size: 14px;">${args.inviterRole}</p>` : ''}
                <p style="margin: 4px 0 0 0; font-weight: 500; color: #2563eb;">${args.mainContractorName}</p>
              </div>

              ${(args.inviterEmail || args.inviterPhone) ? `
                <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                  <p style="margin: 0 0 8px 0; font-weight: 600; color: #1e293b; font-size: 14px;">📞 Kontaktinformasjon:</p>
                  ${args.inviterEmail ? `
                    <p style="margin: 4px 0; color: #64748b; font-size: 14px;">
                      <strong>E-post:</strong> <a href="mailto:${args.inviterEmail}" style="color: #2563eb; text-decoration: none;">${args.inviterEmail}</a>
                    </p>
                  ` : ''}
                  ${args.inviterPhone ? `
                    <p style="margin: 4px 0; color: #64748b; font-size: 14px;">
                      <strong>Telefon:</strong> <a href="tel:${args.inviterPhone}" style="color: #2563eb; text-decoration: none;">${args.inviterPhone}</a>
                    </p>
                  ` : ''}
                  <p style="margin: 8px 0 0 0; color: #64748b; font-size: 12px; font-style: italic;">
                    Ring eller send e-post for spørsmål om prosjektet
                  </p>
                </div>
              ` : ''}

              <p style="margin: 0; font-size: 12px; color: #94a3b8;">
                Denne e-posten ble sendt via JobbLogg prosjektadministrasjonssystem.
              </p>
            </div>
          </div>
        </body>
        </html>
      `;

      const text = `
Prosjektinvitasjon: ${args.projectName}

Hei ${args.subcontractorName},

Du har blitt invitert til å delta som underleverandør på følgende prosjekt:

PROSJEKT: ${args.projectName}
ADRESSE: ${args.projectAddress}
HOVEDENTREPRENØR: ${args.mainContractorName}
INVITERT AV: ${args.inviterName}
DIN ROLLE: ${args.specialization}
ESTIMERT VARIGHET: ${args.estimatedDuration}
PRIORITET: ${formatUrgency(args.urgency)}

${args.projectDescription ? `PROSJEKTBESKRIVELSE:\n${args.projectDescription}\n\n` : ''}
${args.invitationMessage ? `MELDING FRA ${args.inviterName.toUpperCase()}:\n${args.invitationMessage}\n\n` : ''}

Se invitasjon og svar: ${invitationLink}

VIKTIG: Invitasjonen utløper ${formatNorwegianDate(args.expiresAt)}

Med vennlig hilsen,
${args.inviterName}${args.inviterRole ? ` (${args.inviterRole})` : ''}
${args.mainContractorName}

${(args.inviterEmail || args.inviterPhone) ? `
KONTAKTINFORMASJON:
${args.inviterEmail ? `E-post: ${args.inviterEmail}` : ''}
${args.inviterPhone ? `Telefon: ${args.inviterPhone}` : ''}

Ring eller send e-post for spørsmål om prosjektet.
` : ''}
      `;

      // Send email via Resend
      const result = await resend.emails.send({
        from: `${args.mainContractorName} <<EMAIL>>`,
        to: args.subcontractorEmail,
        subject,
        html,
        text,
        tags: [
          { name: 'type', value: 'subcontractor_invitation' },
          { name: 'urgency', value: sanitizeTagValue(args.urgency) },
          { name: 'specialization', value: sanitizeTagValue(args.specialization) },
        ],
        headers: {
          'X-Entity-Ref-ID': args.invitationId,
          'List-Unsubscribe': '<mailto:<EMAIL>>',
          'X-Mailer': 'JobbLogg Project Management System',
        },
      });

      console.log('✅ Subcontractor invitation email sent successfully:', result);

      return {
        success: true,
        emailId: result.data?.id,
        message: 'Invitasjons-email sendt til underleverandør',
      };

    } catch (error) {
      console.error('❌ Failed to send subcontractor invitation email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil',
        message: 'Kunne ikke sende invitasjons-email',
      };
    }
  },
});

// Send response confirmation email to both parties
export const sendResponseConfirmationEmail = action({
  args: {
    invitationId: v.id("projectAssignments"),
    response: v.union(v.literal("accept"), v.literal("decline")),
    responseMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending response confirmation emails for invitation:', args.invitationId);

      // Get invitation details
      const invitation = await ctx.runQuery("subcontractorInvitations:getSubcontractorInvitations" as any, {
        userId: "system", // We'll get the specific invitation by ID
        status: "all"
      });

      // For now, just log the confirmation
      // In a full implementation, we would:
      // 1. Get the invitation details
      // 2. Get both user emails
      // 3. Send confirmation to subcontractor
      // 4. Send notification to main contractor

      console.log('✅ Response confirmation logged:', {
        invitationId: args.invitationId,
        response: args.response,
        responseMessage: args.responseMessage
      });

      return {
        success: true,
        message: 'Bekreftelse registrert',
      };

    } catch (error) {
      console.error('❌ Failed to send response confirmation:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil',
        message: 'Kunne ikke sende bekreftelse',
      };
    }
  },
});

// Test function to verify email sending works for project invitations
export const testProjectInvitationEmail = action({
  args: {
    testEmail: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      console.log('🧪 Testing project invitation email to:', args.testEmail);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL';
      const resend = new Resend(apiKey);

      // Create test invitation data
      const testData = {
        invitationId: 'test-invitation-id',
        subcontractorEmail: args.testEmail,
        subcontractorName: 'Test Underleverandør',
        projectName: 'Test Prosjekt',
        projectAddress: 'Test Adresse 123, 0123 Oslo',
        mainContractorName: 'Test Hovedentreprenør AS',
        inviterName: 'Test Inviter',
        specialization: 'Rørlegger',
        projectDescription: 'Dette er et test prosjekt for å verifisere email-funksjonalitet.',
        estimatedDuration: '2-3 uker',
        invitationMessage: 'Dette er en test-invitasjon for å sjekke at email-systemet fungerer.',
        urgency: 'medium' as const,
        expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days from now
      };

      // Create invitation link
      const invitationLink = `${process.env.SITE_URL || 'http://localhost:5173'}/invitations/${testData.invitationId}`;

      // Generate email content
      const subject = `TEST - Prosjektinvitasjon: ${testData.projectName} - ${testData.mainContractorName}`;

      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🧪 TEST - Prosjektinvitasjon</h1>
            </div>
            <div class="content">
              <p>Hei <strong>${testData.subcontractorName}</strong>,</p>
              <p>Dette er en TEST-email for å verifisere at prosjektinvitasjoner sendes korrekt.</p>

              <h3>Prosjektdetaljer:</h3>
              <ul>
                <li><strong>Prosjekt:</strong> ${testData.projectName}</li>
                <li><strong>Adresse:</strong> ${testData.projectAddress}</li>
                <li><strong>Hovedentreprenør:</strong> ${testData.mainContractorName}</li>
                <li><strong>Din rolle:</strong> ${testData.specialization}</li>
                <li><strong>Estimert varighet:</strong> ${testData.estimatedDuration}</li>
              </ul>

              <p><strong>Melding:</strong> ${testData.invitationMessage}</p>

              <p style="text-align: center;">
                <a href="${invitationLink}" class="button">Se invitasjon (TEST)</a>
              </p>

              <p><em>Utløper: ${formatNorwegianDate(testData.expiresAt)}</em></p>
            </div>
          </div>
        </body>
        </html>
      `;

      const text = `
TEST - Prosjektinvitasjon: ${testData.projectName}

Hei ${testData.subcontractorName},

Dette er en TEST-email for å verifisere at prosjektinvitasjoner sendes korrekt.

PROSJEKTDETALJER:
- Prosjekt: ${testData.projectName}
- Adresse: ${testData.projectAddress}
- Hovedentreprenør: ${testData.mainContractorName}
- Din rolle: ${testData.specialization}
- Estimert varighet: ${testData.estimatedDuration}

Melding: ${testData.invitationMessage}

Se invitasjon: ${invitationLink}

Utløper: ${formatNorwegianDate(testData.expiresAt)}
      `;

      // Send test email
      const result = await resend.emails.send({
        from: `${testData.mainContractorName} <<EMAIL>>`,
        to: args.testEmail,
        subject,
        html,
        text,
        tags: [
          { name: 'type', value: 'test_project_invitation' },
          { name: 'environment', value: 'development' },
        ],
      });

      console.log('✅ Test project invitation email sent successfully:', result);

      return {
        success: true,
        emailId: result.data?.id,
        message: `Test prosjektinvitasjon sendt til ${args.testEmail}`,
        testData,
      };

    } catch (error) {
      console.error('❌ Failed to send test project invitation email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil',
        message: 'Kunne ikke sende test-email',
      };
    }
  },
});
