import { useEffect, useCallback } from 'react';
// import { useMutation } from 'convex/react'; // Unused due to disabled functionality
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../convex/_generated/api'; // Unused due to disabled functionality

/**
 * Hook for tracking user activity
 * Automatically updates login and activity timestamps
 */
export const useUserActivity = () => {
  const { user, isLoaded } = useUser();

  // Safe hook initialization with fallback
  let updateLastLogin: any;
  let updateLastActivity: any;

  try {
    // TODO: Re-enable when type instantiation issues are resolved
    // updateLastLogin = useMutation(api.userActivity.updateLastLogin as any);
    // TODO: Re-enable when type instantiation issues are resolved
    // updateLastActivity = useMutation(api.userActivity.updateLastActivity as any);
  } catch (error) {
    console.warn('Failed to initialize user activity hooks:', error);
    // Provide fallback functions that don't crash
    updateLastLogin = async () => {};
    updateLastActivity = async () => {};
  }

  // Track login when user is first loaded
  useEffect(() => {
    if (isLoaded && user?.id) {
      // Update login timestamp when user is authenticated
      updateLastLogin({ clerkUserId: user.id }).catch((error: any) => {
        console.warn('Failed to update login timestamp:', error);
      });
    }
  }, [isLoaded, user?.id, updateLastLogin]);

  // Function to manually track activity
  const trackActivity = useCallback(async (activityType?: string) => {
    if (!user?.id) return;

    try {
      await updateLastActivity({ 
        clerkUserId: user.id,
        activityType 
      });
    } catch (error) {
      console.warn('Failed to update activity timestamp:', error);
    }
  }, [user?.id, updateLastActivity]);

  return {
    trackActivity,
    isReady: isLoaded && !!user?.id,
  };
};

/**
 * Hook that automatically tracks activity on component mount
 * Use this in components where user performs important actions
 */
export const useAutoTrackActivity = (activityType?: string) => {
  const { trackActivity, isReady } = useUserActivity();

  useEffect(() => {
    if (isReady) {
      trackActivity(activityType);
    }
  }, [isReady, trackActivity, activityType]);

  return { trackActivity };
};
