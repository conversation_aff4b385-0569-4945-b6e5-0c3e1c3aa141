import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../convex/_generated/api';

/**
 * Hook to manage subscription access control
 * Returns subscription status and access permissions
 */
export const useSubscriptionAccess = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();

  // Only query when user is loaded and authenticated
  const shouldQuery = isClerkLoaded && user?.id;

  const subscriptionStatus = useQuery(
    api.subscriptions.getSubscriptionStatus,
    shouldQuery ? { userId: user.id } : "skip"
  );

  const subscription = useQuery(
    api.subscriptions.getUserSubscription,
    shouldQuery ? { userId: user.id } : "skip"
  );

  // Loading state
  const isLoading = !isClerkLoaded || subscriptionStatus === undefined;

  // If no subscription data, return default values
  if (!subscriptionStatus || !subscriptionStatus.hasSubscription) {
    return {
      isLoading,
      subscription: null,
      hasActiveSubscription: false,
      isInTrial: false,
      isTrialExpired: false,
      isInGracePeriod: false,
      canCreateProjects: false,
      canAccessProjects: false,
      hasFullAccess: false,
      isReadOnly: false,
      needsUpgrade: true,
      needsTrialSetup: true,
    };
  }

  // Derive access permissions from subscription status
  const {
    hasActiveSubscription,
    isInTrial,
    isTrialExpired,
    isInGracePeriod,
    canCreateProjects,
    canAccessProjects,
    hasFullAccess,
    isReadOnly,
    needsUpgrade,
  } = subscriptionStatus;

  return {
    isLoading,
    subscription,
    hasActiveSubscription,
    isInTrial,
    isTrialExpired,
    isInGracePeriod,
    canCreateProjects,
    canAccessProjects,
    hasFullAccess,
    isReadOnly,
    needsUpgrade,
    needsTrialSetup: false,
  };
};

/**
 * Hook to get seat management information
 */
export const useSeatManagement = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();

  // Only query when user is loaded and authenticated
  const shouldQuery = isClerkLoaded && user?.id;

  const seatCheck = useQuery(
    api.seatManagement.canInviteTeamMember,
    shouldQuery ? { userId: user.id } : "skip"
  );

  const subscription = useQuery(
    api.subscriptions.getUserSubscription,
    shouldQuery ? { userId: user.id } : "skip"
  );

  const isLoading = !isClerkLoaded || seatCheck === undefined || subscription === undefined;

  if (!seatCheck || !subscription) {
    return {
      isLoading,
      canInvite: false,
      currentSeats: 0,
      maxSeats: 0,
      remainingSeats: 0,
      warning: null,
      warningMessage: null,
      isNearLimit: false,
      isCritical: false,
      isAtLimit: false,
    };
  }

  const currentSeats = subscription.seats || 0;
  const maxSeats = seatCheck.maxSeats || 0;
  const remainingSeats = maxSeats - currentSeats;
  const isAtLimit = currentSeats >= maxSeats;
  const isCritical = seatCheck.warning === "critical";
  const isNearLimit = seatCheck.warning === "approaching" || isCritical;

  return {
    isLoading,
    canInvite: seatCheck.canInvite,
    currentSeats,
    maxSeats,
    remainingSeats,
    warning: seatCheck.warning,
    warningMessage: seatCheck.warningMessage,
    isNearLimit,
    isCritical,
    isAtLimit,
    suggestedPlan: seatCheck.suggestedPlan,
    blockedReason: seatCheck.reason,
    blockedMessage: seatCheck.message,
  };
};
