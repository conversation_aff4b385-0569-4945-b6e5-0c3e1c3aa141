// import { useUser, useAuth } from '@clerk/clerk-react'; // Unused due to disabled functionality
// import { useQuery } from 'convex/react'; // Unused due to disabled functionality
// import { api } from '../../convex/_generated/api'; // Unused due to disabled functionality

/**
 * Hook to get contractor onboarding status
 * Useful for components that need to conditionally render based on onboarding status
 */
export const useContractorOnboardingStatus = () => {
  // const { user, isLoaded: isClerkLoaded } = useUser(); // Unused due to disabled functionality
  // const { isSignedIn, isLoaded: isAuthLoaded } = useAuth(); // Unused due to disabled functionality

  // Only query when both Clerk and Convex auth are ready
  // const shouldQuery = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id; // Unused due to disabled functionality

  // TODO: Re-enable when type instantiation issues are resolved
  // const onboardingStatusResult = useQuery(
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  // const onboardingStatusResult = null; // Temporarily provide fallback - unused

  return {
    isLoading: false, // !shouldQuery || onboardingStatusResult === undefined, // Disabled due to type issues
    exists: false, // onboardingStatusResult?.exists || false, // Disabled due to type issues
    contractorCompleted: false, // onboardingStatusResult?.contractorCompleted || false, // Disabled due to type issues
    contractorCompanyId: null, // onboardingStatusResult?.contractorCompanyId || null, // Disabled due to type issues
    authError: false, // onboardingStatusResult?.authError || false, // Disabled due to type issues
    error: null, // onboardingStatusResult?.error || null, // Disabled due to type issues
  };
};

/**
 * Hook to get contractor company information
 * Returns the contractor's company data if available
 */
export const useContractorCompany = () => {
  // const { user, isLoaded: isClerkLoaded } = useUser(); // Unused due to disabled functionality
  // const { isSignedIn, isLoaded: isAuthLoaded } = useAuth(); // Unused due to disabled functionality

  // Only query when both Clerk and Convex auth are ready
  // const shouldQuery = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id; // Unused due to disabled functionality

  // TODO: Re-enable when type instantiation issues are resolved
  // const contractorCompanyResult = useQuery(
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  // const contractorCompanyResult = null; // Temporarily provide fallback - unused

  return {
    isLoading: false, // !shouldQuery || contractorCompanyResult === undefined, // Disabled due to type issues
    company: null, // contractorCompanyResult?.company || null, // Disabled due to type issues
    authError: false, // contractorCompanyResult?.authError || false, // Disabled due to type issues
    error: null, // contractorCompanyResult?.error || null, // Disabled due to type issues
  };
};
