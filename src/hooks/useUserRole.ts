import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../convex/_generated/api';

/**
 * Hook to get current user's role and team information
 * Returns user role (administrator/utfoerende) and team context
 */
export const useUserRole = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();

  // Only query when user is loaded and authenticated
  const shouldQuery = isClerkLoaded && user?.id;

  // Use safe version to avoid errors when user doesn't exist yet
  const userWithRole = useQuery(
    api.contractorOnboardingSafe.getContractorOnboardingStatusSafe,
    shouldQuery ? { clerkUserId: user.id } : "skip"
  );

  // If user doesn't exist or has auth error, return safe defaults
  if (!userWithRole || userWithRole.authError || !userWithRole.exists) {
    return {
      isLoading: !isClerkLoaded || userWithRole === undefined,
      user: null,
      role: null,
      isAdministrator: false,
      isProsjektleder: false,
      isUtfoerende: false,
      contractorCompanyId: userWithRole?.contractorCompanyId || null,
      hasCompletedOnboarding: userWithRole?.contractorCompleted || false,
    };
  }

  // For now, default all existing users to administrator role
  // This will be updated when proper role management is implemented
  const role = "administrator";

  return {
    isLoading: !isClerkLoaded || userWithRole === undefined,
    user: userWithRole,
    role,
    isAdministrator: role === "administrator",
    isProsjektleder: role === "prosjektleder", 
    isUtfoerende: role === "utfoerende",
    contractorCompanyId: userWithRole.contractorCompanyId,
    hasCompletedOnboarding: userWithRole.contractorCompleted,
  };
};

/**
 * Hook to get team members (for administrators and prosjektleder)
 */
export const useTeamMembers = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator, isProsjektleder, hasCompletedOnboarding } = useUserRole();

  // Only query if user is administrator or prosjektleder AND has completed onboarding
  const shouldQuery = isClerkLoaded && user?.id && (isAdministrator || isProsjektleder) && hasCompletedOnboarding;

  // For now, return empty array if conditions aren't met
  // This avoids the "user not found" error until the user completes onboarding
  const teamMembers = shouldQuery ? useQuery(
    api.teamManagement.getTeamMembers,
    { clerkUserId: user.id }
  ) : [];

  return {
    isLoading: shouldQuery && teamMembers === undefined,
    teamMembers: Array.isArray(teamMembers) ? teamMembers : [],
    canManageTeam: isAdministrator && hasCompletedOnboarding,
    canViewTeam: (isAdministrator || isProsjektleder) && hasCompletedOnboarding,
  };
};

/**
 * Hook to get pending invitations (only for administrators)
 */
export const usePendingInvitations = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator, hasCompletedOnboarding } = useUserRole();

  // Only query if user is administrator AND has completed onboarding
  const shouldQuery = isClerkLoaded && user?.id && isAdministrator && hasCompletedOnboarding;

  // For now, return empty array if conditions aren't met
  // This avoids the "user not found" error until the user completes onboarding
  const pendingInvitations = shouldQuery ? useQuery(
    api.teamManagement.getPendingInvitations,
    { clerkUserId: user.id }
  ) : [];

  return {
    isLoading: shouldQuery && pendingInvitations === undefined,
    invitations: Array.isArray(pendingInvitations) ? pendingInvitations : [],
    canManageInvitations: isAdministrator && hasCompletedOnboarding,
  };
};

/**
 * Hook to get team workload overview (only for administrators)
 */
export const useTeamWorkload = () => {
  // const { user, isLoaded: isClerkLoaded } = useUser();
  // const { isAdministrator } = useUserRole();

  // Only query if user is administrator
  // const shouldQuery = isClerkLoaded && user?.id && isAdministrator;

  // const workloadData = useQuery(
  //   api.teamManagement.getTeamWorkloadOverview as any,
  //   shouldQuery ? { requestedBy: user.id } : "skip"
  // );
  // const workloadData = null; // Temporarily provide fallback - unused

  return {
    isLoading: false, // shouldQuery && workloadData === undefined, // Disabled due to type issues
    workload: null, // workloadData, // Disabled due to type issues
    canViewWorkload: false, // isAdministrator, // Disabled due to type issues
  };
};

/**
 * Hook to check if current user can access team management features
 */
export const useCanManageTeam = () => {
  const { isAdministrator, isLoading } = useUserRole();

  return {
    canManageTeam: isAdministrator,
    isLoading,
  };
};
