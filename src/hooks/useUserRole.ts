import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../convex/_generated/api';

/**
 * Hook to get current user's role and team information
 * Returns user role (administrator/utfoerende) and team context
 */
export const useUserRole = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();

  // Only query when user is loaded and authenticated
  const shouldQuery = isClerkLoaded && user?.id;

  const userWithRole = useQuery(
    api.teamManagement.getUserWithRole,
    shouldQuery ? { clerkUserId: user.id } : "skip"
  );

  return {
    isLoading: !isClerkLoaded || userWithRole === undefined,
    user: userWithRole,
    role: userWithRole?.role || null,
    isAdministrator: userWithRole?.role === "administrator",
    isProsjektleder: userWithRole?.role === "prosjektleder",
    isUtfoerende: userWithRole?.role === "utfoerende",
    contractorCompanyId: userWithRole?.contractorCompanyId || null,
    hasCompletedOnboarding: userWithRole?.contractorCompleted || false,
  };
};

/**
 * Hook to get team members (for administrators and prosjektleder)
 */
export const useTeamMembers = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator, isProsjektleder } = useUserRole();

  // Only query if user is administrator or prosjektleder
  const shouldQuery = isClerkLoaded && user?.id && (isAdministrator || isProsjektleder);

  const teamMembers = useQuery(
    api.teamManagement.getTeamMembers,
    shouldQuery ? { clerkUserId: user.id } : "skip"
  );

  return {
    isLoading: shouldQuery && teamMembers === undefined,
    teamMembers: teamMembers || [],
    canManageTeam: isAdministrator,
    canViewTeam: isAdministrator || isProsjektleder,
  };
};

/**
 * Hook to get pending invitations (only for administrators)
 */
export const usePendingInvitations = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator } = useUserRole();

  // Only query if user is administrator
  const shouldQuery = isClerkLoaded && user?.id && isAdministrator;

  const pendingInvitations = useQuery(
    api.teamManagement.getPendingInvitations,
    shouldQuery ? { clerkUserId: user.id } : "skip"
  );

  return {
    isLoading: shouldQuery && pendingInvitations === undefined,
    invitations: pendingInvitations || [],
    canManageInvitations: isAdministrator,
  };
};

/**
 * Hook to get team workload overview (only for administrators)
 */
export const useTeamWorkload = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator } = useUserRole();

  // Only query if user is administrator
  const shouldQuery = isClerkLoaded && user?.id && isAdministrator;

  const workloadData = useQuery(
    api.teamManagement.getTeamWorkloadOverview,
    shouldQuery ? { requestedBy: user.id } : "skip"
  );

  return {
    isLoading: shouldQuery && workloadData === undefined,
    workload: workloadData,
    canViewWorkload: isAdministrator,
  };
};

/**
 * Hook to check if current user can access team management features
 */
export const useCanManageTeam = () => {
  const { isAdministrator, isLoading } = useUserRole();

  return {
    canManageTeam: isAdministrator,
    isLoading,
  };
};
