// import { useQuery } from 'convex/react'; // Unused due to disabled functionality
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../convex/_generated/api'; // Unused due to disabled functionality

/**
 * Hook to get current user's role and team information
 * Returns user role (administrator/utfoerende) and team context
 */
export const useUserRole = () => {
  const { isLoaded: isClerkLoaded } = useUser(); // user unused due to disabled functionality

  // Only query when user is loaded and authenticated
  // const shouldQuery = isClerkLoaded && user?.id; // Unused due to disabled functionality

  // TODO: Re-enable when type instantiation issues are resolved
  // const userWithRole = useQuery(
  //   api.teamManagement.getUserWithRole,
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  const userWithRole = null; // Temporarily provide fallback

  return {
    isLoading: !isClerkLoaded || userWithRole === undefined,
    user: userWithRole,
    role: null, // userWithRole?.role || null, // Disabled due to type issues
    isAdministrator: false, // userWithRole?.role === "administrator", // Disabled due to type issues
    isProsjektleder: false, // userWithRole?.role === "prosjektleder", // Disabled due to type issues
    isUtfoerende: false, // userWithRole?.role === "utfoerende", // Disabled due to type issues
    contractorCompanyId: null, // userWithRole?.contractorCompanyId || null, // Disabled due to type issues
    hasCompletedOnboarding: false, // userWithRole?.contractorCompleted || false, // Disabled due to type issues
  };
};

/**
 * Hook to get team members (for administrators and prosjektleder)
 */
export const useTeamMembers = () => {
  // const { user, isLoaded: isClerkLoaded } = useUser(); // Unused due to disabled functionality
  // const { isAdministrator, isProsjektleder } = useUserRole(); // Unused due to disabled functionality

  // Only query if user is administrator or prosjektleder
  // const shouldQuery = isClerkLoaded && user?.id && (isAdministrator || isProsjektleder); // Unused due to disabled functionality

  // TODO: Re-enable when type instantiation issues are resolved
  // const teamMembers = useQuery(
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  // const teamMembers = null; // Temporarily provide fallback - unused

  return {
    isLoading: false, // shouldQuery && teamMembers === undefined, // Disabled due to type issues
    teamMembers: [], // teamMembers || [], // Disabled due to type issues
    canManageTeam: false, // isAdministrator, // Disabled due to type issues
    canViewTeam: false, // isAdministrator || isProsjektleder, // Disabled due to type issues
  };
};

/**
 * Hook to get pending invitations (only for administrators)
 */
export const usePendingInvitations = () => {
  // const { user, isLoaded: isClerkLoaded } = useUser(); // Unused due to disabled functionality
  const { isAdministrator } = useUserRole();

  // Only query if user is administrator
  // TODO: Re-enable when type instantiation issues are resolved
  // const shouldQuery = isClerkLoaded && user?.id && isAdministrator;

  // const pendingInvitations = useQuery(
  //   api.teamManagement.getPendingInvitations as any,
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  // const pendingInvitations = null; // Temporarily provide fallback - unused

  return {
    isLoading: false, // shouldQuery && pendingInvitations === undefined, // Disabled due to type issues
    invitations: [], // pendingInvitations || [], // Disabled due to type issues
    canManageInvitations: isAdministrator,
  };
};

/**
 * Hook to get team workload overview (only for administrators)
 */
export const useTeamWorkload = () => {
  // TODO: Re-enable when type instantiation issues are resolved
  // const { user, isLoaded: isClerkLoaded } = useUser();
  // const { isAdministrator } = useUserRole();

  // Only query if user is administrator
  // const shouldQuery = isClerkLoaded && user?.id && isAdministrator;

  // const workloadData = useQuery(
  //   api.teamManagement.getTeamWorkloadOverview as any,
  //   shouldQuery ? { requestedBy: user.id } : "skip"
  // );
  // const workloadData = null; // Temporarily provide fallback - unused

  return {
    isLoading: false, // shouldQuery && workloadData === undefined, // Disabled due to type issues
    workload: null, // workloadData, // Disabled due to type issues
    canViewWorkload: false, // isAdministrator, // Disabled due to type issues
  };
};

/**
 * Hook to check if current user can access team management features
 */
export const useCanManageTeam = () => {
  const { isAdministrator, isLoading } = useUserRole();

  return {
    canManageTeam: isAdministrator,
    isLoading,
  };
};
