import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { Authenticated, Unauthenticated, AuthLoading } from 'convex/react'
import { PWAInstallBanner } from './components/PWAInstallBanner'
import { OfflineSync, StorageUsage } from './components/OfflineSync'
import { ContractorOnboardingGuardSimple } from './components/ContractorOnboardingGuardSimple'
import { CookieBanner } from './components/CookieConsent'
import { AuthenticatedLayout } from './components/ui/Layout'
import TestCompanyProfile from './pages/TestCompanyProfile'
import {
  LazyDashboard,
  LazyCreateProject,
  LazyProjectLog,
  LazyProjectDetail,
  LazyProjectEdit,
  LazySignIn,
  LazySignUp,
  LazySharedProject,
  LazyConversations,
  LazyArchivedProjects,
  LazyGoogleMapsTest,
  LazyContractorOnboarding,
  LazyTeamManagement,
  LazyTeamProjects,
  LazyTeamOnboarding,
  LazyAcceptInvite,
  LazyBlockedUser,
  LazyInvitationsList,
  LazyInvitationDetail,
  LazyNotifications,
  LazySubscriptionManagement,
  LazyPrivacyPolicy,
  LazyCookiePolicy,
  LazyTermsOfService,
  LazyPrivacySettings,
  LazyHelpPage,
  LazyLandingPage,
  LazyPricingPage,
  LazyPageWrapper,
  preloadCriticalRoutes
} from './components/LazyComponents'
import { useEffect } from 'react'

function App() {
  // Preload critical routes on app initialization
  useEffect(() => {
    preloadCriticalRoutes();
  }, []);

  return (
    <BrowserRouter>
      <div className="min-h-screen bg-white transition-colors duration-300">
        {/* PWA Components */}
        <PWAInstallBanner />
        <OfflineSync />
        <StorageUsage />

        {/* Cookie Consent */}
        <CookieBanner />
        <Routes>
          {/* Public Routes - Accessible to everyone */}
          <Route path="/shared/:sharedId" element={
            <LazyPageWrapper>
              <LazySharedProject />
            </LazyPageWrapper>
          } />
          {/* Privacy and Legal Pages - Publicly accessible */}
          <Route path="/privacy-policy" element={
            <LazyPageWrapper>
              <LazyPrivacyPolicy />
            </LazyPageWrapper>
          } />
          <Route path="/cookie-policy" element={
            <LazyPageWrapper>
              <LazyCookiePolicy />
            </LazyPageWrapper>
          } />
          <Route path="/terms-of-service" element={
            <LazyPageWrapper>
              <LazyTermsOfService />
            </LazyPageWrapper>
          } />
          <Route path="/help" element={
            <LazyPageWrapper>
              <LazyHelpPage />
            </LazyPageWrapper>
          } />
          <Route path="/pricing" element={
            <LazyPageWrapper>
              <LazyPricingPage />
            </LazyPageWrapper>
          } />

          {/* Authentication Routes - Accessible to unauthenticated users */}
          <Route path="/sign-in" element={
            <LazyPageWrapper>
              <LazySignIn />
            </LazyPageWrapper>
          } />
          <Route path="/sign-up" element={
            <LazyPageWrapper>
              <LazySignUp />
            </LazyPageWrapper>
          } />

          {/* Team Invitation - Publicly accessible (handles auth internally) */}
          <Route path="/team-onboarding" element={
            <LazyPageWrapper>
              <LazyTeamOnboarding />
            </LazyPageWrapper>
          } />

          {/* Magic Link Accept Invitation - Publicly accessible */}
          <Route path="/accept-invite/*" element={
            <LazyPageWrapper>
              <LazyAcceptInvite />
            </LazyPageWrapper>
          } />

          {/* Contractor Onboarding - Requires authentication but bypasses onboarding guard */}
          <Route path="/contractor-onboarding/*" element={
            <>
              <Authenticated>
                <LazyPageWrapper>
                  <LazyContractorOnboarding />
                </LazyPageWrapper>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Main Route - Landing page for unauthenticated, Dashboard for authenticated */}
          <Route path="/" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyDashboard />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <LazyPageWrapper>
                  <LazyLandingPage />
                </LazyPageWrapper>
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/archived-projects" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyArchivedProjects />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/conversations" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyConversations />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/team" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyTeamManagement />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/team/projects" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyTeamProjects />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Privacy Settings - Protected route */}
          <Route path="/privacy-settings" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyPrivacySettings />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Blocked User Page - Only accessible to authenticated blocked users */}
          <Route path="/blocked" element={
            <>
              <Authenticated>
                <LazyPageWrapper>
                  <LazyBlockedUser />
                </LazyPageWrapper>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Invitation Management Routes */}
          <Route path="/invitations" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyInvitationsList />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/invitations/:invitationId" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyInvitationDetail />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/notifications" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyNotifications />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/subscription" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazySubscriptionManagement />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/create" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyCreateProject />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/create-wizard" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyCreateProject />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/test-google-maps" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyGoogleMapsTest />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/project/:projectId/details" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyProjectDetail />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/project/:projectId/edit" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyProjectEdit />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/project/:projectId" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyProjectLog />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          {/* Remove chat route - now using embedded chat */}

          {/* Test Routes - Development only */}
          <Route path="/test-company-profile" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <TestCompanyProfile />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
        </Routes>
      </div>
    </BrowserRouter>
  )
}

export default App
