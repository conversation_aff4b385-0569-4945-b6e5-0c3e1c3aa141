import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery, useAction } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, StepProgress, WizardStep } from '../../components/ui';
import { Step1ProjectDetails } from './steps/Step1ProjectDetails';
import { Step2CustomerInfo } from './steps/Step2CustomerInfo';
import { Step3JobDescription } from './steps/Step3JobDescription';
import { useOfflineProjects } from '../../hooks/useOfflineProjects';
import { usePWA } from '../../hooks/usePWA';
import { OfflineFeatureGuard, OfflineModeBanner } from '../../components/OfflineFeatureGuard';
import { SubscriptionGate } from '../../components/subscription';

const STORAGE_KEY = 'jobblogg-create-project-wizard';

// Form data interface for the multi-step wizard
export interface WizardFormData {
  // Project details
  projectName: string;
  description: string;

  // Customer data
  customerName: string;
  customerType: 'privat' | 'bedrift';
  contactPerson: string;
  phone: string;
  email: string;
  // Enhanced address fields
  address: string;           // Legacy field for backward compatibility
  streetAddress: string;     // New structured address fields
  postalCode: string;
  city: string;
  entrance: string;
  orgNumber: string;
  notes: string;

  // Job description
  jobDescription: string;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;

  // Image data for persistence
  selectedImages: string[]; // Base64 encoded images for localStorage persistence
  imageFiles: File[]; // Actual file objects (not persisted, recreated from base64)

  // Customer notification settings
  sendCustomerNotification: boolean; // Whether to send automated email to customer
}

// Step validation interface
export interface StepValidation {
  isValid: boolean;
  errors: { [key: string]: string };
}



const CreateProjectWizard: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [useExistingCustomer, setUseExistingCustomer] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>('');
  const [createdProjectId, setCreatedProjectId] = useState<string | null>(null);
  const [customerNotificationSent, setCustomerNotificationSent] = useState(false);
  const [countdown, setCountdown] = useState(6);

  // Offline functionality
  const { isOnline, canAccessOfflineData } = usePWA();
  const { createProjectOffline } = useOfflineProjects();

  // Brønnøysundregisteret data tracking
  const [brregData, setBrregData] = useState<any>(null);
  const [brregFetchedAt, setBrregFetchedAt] = useState<number | null>(null);
  const [useCustomAddress, setUseCustomAddress] = useState(false);

  // Field locking state
  const [lockedFields, setLockedFields] = useState({
    orgNumber: false,
    address: false
  });

  // Track if company has been selected from Brønnøysundregisteret
  const [companySelected, setCompanySelected] = useState(false);

  // Store managing director info for reference (not editable)
  const [managingDirectorInfo, setManagingDirectorInfo] = useState<string>('');

  const [formData, setFormData] = useState<WizardFormData>({
    projectName: '',
    description: '',
    customerName: '',
    customerType: 'privat',
    contactPerson: '',
    phone: '',
    email: '',
    address: '',
    streetAddress: '',
    postalCode: '',
    city: '',
    entrance: '',
    orgNumber: '',
    notes: '',
    jobDescription: '',
    accessNotes: '',
    equipmentNeeds: '',
    unresolvedQuestions: '',
    personalNotes: '',
    selectedImages: [],
    imageFiles: [],
    sendCustomerNotification: true // Default to true for better customer engagement
  });

  const navigate = useNavigate();
  const createProject = useMutation(api.projects.create);
  const createCustomer = useMutation(api.customers.create);
  const updateProjectJobData = useMutation(api.projects.updateProjectJobData);
  const generateUploadUrl = useMutation(api.projects.generateUploadUrl);
  const storeJobImage = useMutation(api.projects.storeJobImage);
  const sendCustomerNotificationEmail = useAction(api.emails.sendCustomerNotificationEmail);
  const updateSharingSettings = useMutation(api.projects.updateSharingSettings);
  const { user } = useUser();

  // Fetch existing customers for selection
  const existingCustomers = useQuery(
    api.customers.getByUser,
    user?.id ? { userId: user.id } : "skip"
  );

  // Define wizard steps
  const steps = [
    {
      id: 1,
      title: 'Prosjektdetaljer',
      description: 'Grunnleggende prosjektinformasjon',
      isCompleted: currentStep > 1,
      isActive: currentStep === 1
    },
    {
      id: 2,
      title: 'Kundeinformasjon',
      description: 'Kundedetaljer og kontaktinformasjon',
      isCompleted: currentStep > 2,
      isActive: currentStep === 2
    },
    {
      id: 3,
      title: 'Jobbeskrivelse',
      description: 'Detaljert jobbinformasjon og krav',
      isCompleted: currentStep > 3,
      isActive: currentStep === 3
    }
  ];

  // Autosave functionality with debouncing
  const saveToLocalStorage = useCallback(() => {
    const dataToSave = {
      formData,
      currentStep,
      useExistingCustomer,
      selectedCustomerId,
      createdProjectId,
      // Brønnøysundregisteret data tracking
      brregData,
      brregFetchedAt,
      useCustomAddress,
      lockedFields,
      companySelected,
      managingDirectorInfo,
      timestamp: Date.now()
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
  }, [formData, currentStep, useExistingCustomer, selectedCustomerId, createdProjectId, brregData, brregFetchedAt, useCustomAddress, lockedFields, companySelected, managingDirectorInfo]);

  // Check if form has meaningful data for draft creation
  const hasFormData = useCallback(() => {
    return formData.projectName.trim().length > 0 ||
           formData.description.trim().length > 0 ||
           formData.customerName.trim().length > 0 ||
           formData.jobDescription.trim().length > 0;
  }, [formData]);

  // Debounced save function
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveToLocalStorage();
    }, 500); // 500ms debounce delay

    return () => clearTimeout(timeoutId);
  }, [saveToLocalStorage]);

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        // Only restore if data is less than 24 hours old
        if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {
          // Restore form data
          if (parsed.formData) {
            setFormData(parsed.formData);
          }

          // Restore wizard state
          if (typeof parsed.currentStep === 'number') {
            setCurrentStep(parsed.currentStep);
          }
          if (typeof parsed.useExistingCustomer === 'boolean') {
            setUseExistingCustomer(parsed.useExistingCustomer);
          }
          if (parsed.selectedCustomerId) {
            setSelectedCustomerId(parsed.selectedCustomerId);
          }
          if (parsed.createdProjectId) {
            setCreatedProjectId(parsed.createdProjectId);
          }

          // Restore Brønnøysundregisteret data tracking
          if (parsed.brregData) {
            setBrregData(parsed.brregData);
          }
          if (typeof parsed.brregFetchedAt === 'number') {
            setBrregFetchedAt(parsed.brregFetchedAt);
          }
          if (typeof parsed.useCustomAddress === 'boolean') {
            setUseCustomAddress(parsed.useCustomAddress);
          }
          if (parsed.lockedFields) {
            setLockedFields(parsed.lockedFields);
          }
          if (typeof parsed.companySelected === 'boolean') {
            setCompanySelected(parsed.companySelected);
          }
          if (parsed.managingDirectorInfo) {
            setManagingDirectorInfo(parsed.managingDirectorInfo);
          }
        }
      } catch (error) {
        console.error('Error loading saved wizard data:', error);
      }
    }
  }, []);

  // Handle page unload - ensure draft is saved if user has entered data
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasFormData()) {
        // Save current state as draft before leaving
        saveToLocalStorage();

        // Show browser confirmation dialog
        e.preventDefault();
        e.returnValue = 'Du har ulagrede endringer. Er du sikker på at du vil forlate siden?';
        return e.returnValue;
      }
    };

    const handleUnload = () => {
      if (hasFormData()) {
        // Final save attempt when page is actually unloading
        saveToLocalStorage();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, [hasFormData, saveToLocalStorage]);

  // Clear localStorage on successful submission
  const clearSavedData = () => {
    localStorage.removeItem(STORAGE_KEY);
  };

  // Auto-close success modal with countdown
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (showSuccess && countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else if (showSuccess && countdown === 0) {
      setShowSuccess(false);
      navigate('/');
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [showSuccess, countdown, navigate]);

  // Reset countdown when success modal is shown
  useEffect(() => {
    if (showSuccess) {
      setCountdown(6);
    }
  }, [showSuccess]);

  // Update form data
  const updateFormData = (updates: Partial<WizardFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < 3) {
      // Save data immediately before navigating to ensure no data loss
      saveToLocalStorage();
      setCurrentStep(currentStep + 1);
      setErrors({});
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      // Save data immediately before navigating to ensure no data loss
      saveToLocalStorage();
      setCurrentStep(currentStep - 1);
      setErrors({});
    }
  };

  return (
    <SubscriptionGate feature="create_project">
      <PageLayout
        title="Opprett nytt prosjekt"
        showBackButton
        backUrl="/"
        showFooter={false}
      >
        <OfflineFeatureGuard feature="create-project">
      {/* Success Message */}
      {showSuccess && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-8 max-w-md mx-4 text-center animate-scale-up">
            <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-success-soft rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-jobblogg-text-strong mb-2">Prosjekt opprettet!</h3>
            <p className="text-jobblogg-text-muted mb-4">Ditt nye prosjekt er nå klart for bruk.</p>

            {/* Customer notification status */}
            {customerNotificationSent && (
              <div className="bg-jobblogg-accent-soft border border-jobblogg-accent/20 rounded-lg p-4 text-left mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">📧</span>
                  <span className="text-sm font-medium text-jobblogg-text-strong">
                    Kunde varslet automatisk
                  </span>
                </div>
                <p className="text-sm text-jobblogg-text-medium">
                  Kunden har mottatt en e-post med lenke til prosjektet og kan nå følge fremdriften i sanntid.
                </p>
              </div>
            )}

            {/* Manual dismiss button with countdown */}
            <button
              onClick={() => {
                setShowSuccess(false);
                navigate('/');
              }}
              className="w-full bg-jobblogg-primary hover:bg-jobblogg-primary-hover text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2"
            >
              Lukk og gå til oversikt ({countdown}s)
            </button>
          </div>
        </div>
      )}

      {/* Offline Mode Banner */}
      <OfflineModeBanner className="mb-6" />

      {/* Main Wizard Content */}
      <div className="max-w-4xl mx-auto">
        {/* Step Progress */}
        <StepProgress steps={steps} currentStep={currentStep} className="mb-8" />

        {/* Wizard Form */}
        <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border overflow-hidden">
          <div className="p-8">
            {/* Step 1: Project Details */}
            <WizardStep
              title="Prosjektdetaljer"
              description="Start med å beskrive prosjektet ditt"
              isActive={currentStep === 1}
            >
              <Step1ProjectDetails
                formData={formData}
                updateFormData={updateFormData}
                errors={errors}
                onNext={goToNextStep}
              />
            </WizardStep>

            {/* Step 2: Customer Information */}
            <WizardStep
              title="Kundeinformasjon"
              description="Legg til kundedetaljer for prosjektet"
              isActive={currentStep === 2}
            >
              <Step2CustomerInfo
                formData={formData}
                updateFormData={updateFormData}
                errors={errors}
                useExistingCustomer={useExistingCustomer}
                setUseExistingCustomer={setUseExistingCustomer}
                selectedCustomerId={selectedCustomerId}
                setSelectedCustomerId={setSelectedCustomerId}
                existingCustomers={existingCustomers}
                onNext={goToNextStep}
                onPrevious={goToPreviousStep}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
                setErrors={setErrors}
                createProject={createProject}
                createCustomer={createCustomer}
                sendCustomerNotificationEmail={sendCustomerNotificationEmail}
                updateSharingSettings={updateSharingSettings}
                user={user}
                clearSavedData={clearSavedData}
                setShowSuccess={setShowSuccess}
                navigate={navigate}
                setCreatedProjectId={setCreatedProjectId}
                // Brønnøysundregisteret data tracking
                brregData={brregData}
                setBrregData={setBrregData}
                brregFetchedAt={brregFetchedAt}
                setBrregFetchedAt={setBrregFetchedAt}
                useCustomAddress={useCustomAddress}
                setUseCustomAddress={setUseCustomAddress}
                lockedFields={lockedFields}
                setLockedFields={setLockedFields}
                companySelected={companySelected}
                setCompanySelected={setCompanySelected}
                managingDirectorInfo={managingDirectorInfo}
                setManagingDirectorInfo={setManagingDirectorInfo}
              />
            </WizardStep>

            {/* Step 3: Job Description */}
            <WizardStep
              title="Jobbeskrivelse"
              description="Beskriv jobben som skal utføres"
              isActive={currentStep === 3}
            >
              <Step3JobDescription
                formData={formData}
                updateFormData={updateFormData}
                errors={errors}
                onPrevious={goToPreviousStep}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
                setErrors={setErrors}
                updateProjectJobData={updateProjectJobData}
                generateUploadUrl={generateUploadUrl}
                storeJobImage={storeJobImage}
                createProject={createProject}
                createCustomer={createCustomer}
                sendCustomerNotificationEmail={sendCustomerNotificationEmail}
                updateSharingSettings={updateSharingSettings}
                user={user}
                clearSavedData={clearSavedData}
                setShowSuccess={setShowSuccess}
                navigate={navigate}
                createdProjectId={createdProjectId}
                setCustomerNotificationSent={setCustomerNotificationSent}
                useExistingCustomer={useExistingCustomer}
                selectedCustomerId={selectedCustomerId}
                // Offline functionality
                createProjectOffline={createProjectOffline}
                isOnline={isOnline}
                canAccessOfflineData={canAccessOfflineData}
                // Brønnøysundregisteret data tracking
                brregData={brregData}
                brregFetchedAt={brregFetchedAt}
                useCustomAddress={useCustomAddress}
              />
            </WizardStep>
          </div>
        </div>
      </div>
        </OfflineFeatureGuard>
      </PageLayout>
    </SubscriptionGate>
  );
};

export default CreateProjectWizard;


