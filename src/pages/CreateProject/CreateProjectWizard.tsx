import React from 'react';
import { PageLayout, Heading2, BodyText } from '../../components/ui';

const CreateProjectWizard: React.FC = () => {
  return (
    <PageLayout containerWidth="narrow">
      <div className="w-full px-4 sm:px-0 sm:max-w-2xl sm:mx-auto space-y-6 sm:space-y-8">
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Opprett nytt prosjekt</Heading2>
          <BodyText>
            Prosjektopprettelse er midlertidig deaktivert på grunn av tekniske problemer med TypeScript-kompilering.
            Funksjonaliteten vil bli gjenopprettet så snart type instantiation-problemene er løst.
          </BodyText>
        </div>
      </div>
    </PageLayout>
  );
};

export default CreateProjectWizard;
