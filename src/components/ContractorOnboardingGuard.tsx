import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
// import { useQuery } from 'convex/react'; // Unused due to disabled functionality
import { useUser, useAuth } from '@clerk/clerk-react';
// import { api } from '../../convex/_generated/api'; // Unused due to disabled functionality


interface ContractorOnboardingGuardProps {
  children: React.ReactNode;
}

/**
 * Route protection component that ensures contractors complete onboarding
 * before accessing the main application.
 * 
 * Flow:
 * 1. Check if user is authenticated (Clerk)
 * 2. Check contractor onboarding status from Convex
 * 3. Redirect to onboarding if incomplete
 * 4. Allow access to main app if complete
 */
export const ContractorOnboardingGuard: React.FC<ContractorOnboardingGuardProps> = ({ children }) => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();
  const location = useLocation();
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const [hasTriedCreatingUser, setHasTriedCreatingUser] = useState(false);

  // Mutation to create user if it doesn't exist (safe version)
  // TODO: Re-enable when type instantiation issues are resolved
  // const getOrCreateUserSafe = useMutation(api.contractorOnboardingSafe.getOrCreateUserSafe);

  // Only query contractor onboarding status when both Clerk and Convex auth are ready
  const shouldQueryOnboarding = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;

  // Use safe version that doesn't throw auth errors
  // TODO: Re-enable when type instantiation issues are resolved
  // const onboardingStatusResult = useQuery(
  //   shouldQueryOnboarding ? { clerkUserId: user.id } : "skip"
  // );
  const onboardingStatusResult = null; // Temporarily provide fallback

  // Extract the actual status from the safe result
  const onboardingStatus = (onboardingStatusResult as any) && !(onboardingStatusResult as any).authError ? {
    exists: (onboardingStatusResult as any).exists,
    contractorCompleted: (onboardingStatusResult as any).contractorCompleted,
    contractorCompanyId: (onboardingStatusResult as any).contractorCompanyId
  } : undefined;

  // Handle loading states and onboarding check with retry mechanism and user creation
  useEffect(() => {
    if (isClerkLoaded && isAuthLoaded) {
      if (!isSignedIn || !user) {
        setIsCheckingOnboarding(false);
      } else if (onboardingStatusResult !== undefined) {
        // We got a result (either success or auth error)
        if ((onboardingStatusResult as any)?.authError) {
          // Auth error usually means user doesn't exist in Convex - create immediately
          if (!hasTriedCreatingUser && user?.id) {
            console.log("🔄 User not found in Convex, creating user record for:", user.id);
            setHasTriedCreatingUser(true);
            // TODO: Re-enable when getOrCreateUserSafe is available
            // getOrCreateUserSafe({ clerkUserId: user.id })
            //   .then((result: any) => {
            Promise.resolve({ success: false, error: "Temporarily disabled" })
              .then((result: any) => {
                if (result.authError) {
                  console.error("❌ Failed to create user record:", result.error);
                  setIsCheckingOnboarding(false);
                } else {
                  console.log("✅ User record created/retrieved successfully:", result.user);
                  // Reset states to allow query to re-run with new user
                  setRetryCount(0);
                  setHasTriedCreatingUser(false);
                }
              })
              .catch((error: any) => {
                console.error("❌ Failed to create user record:", error);
                setIsCheckingOnboarding(false);
              });
          } else if (retryCount < 3) {
            // Retry the query after user creation attempt
            console.log(`🔄 Retrying query (${retryCount + 1}/3)...`);
            const timer = setTimeout(() => {
              setRetryCount(prev => prev + 1);
            }, 1000); // 1 second delay

            return () => clearTimeout(timer);
          } else {
            // Exhausted retries
            console.error("❌ Exhausted retries, giving up");
            setIsCheckingOnboarding(false);
          }
        } else {
          // Success - user exists and we have data
          console.log("✅ User data loaded successfully:", onboardingStatusResult);
          setIsCheckingOnboarding(false);
          setRetryCount(0);
        }
      }
    }
  }, [isClerkLoaded, isAuthLoaded, isSignedIn, user, onboardingStatusResult, shouldQueryOnboarding, retryCount, hasTriedCreatingUser]); // getOrCreateUserSafe removed due to disabled functionality

  // Show loading state while checking authentication and onboarding
  if (!isClerkLoaded || !isAuthLoaded || isCheckingOnboarding) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Laster JobbLogg...
            </h2>
            <p className="text-jobblogg-text-muted">
              {!isClerkLoaded ? 'Starter autentisering...' :
               !isAuthLoaded ? 'Synkroniserer med server...' :
               hasTriedCreatingUser ? 'Oppretter brukeroppføring...' :
               retryCount > 0 ? `Prøver igjen (${retryCount}/2)...` :
               'Sjekker brukerinformasjon...'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If user is not authenticated, let Clerk handle the redirect
  if (!isSignedIn || !user) {
    return <Navigate to="/sign-in" replace />;
  }

  // If onboarding status is not loaded yet and we haven't exhausted retries, show loading
  if (onboardingStatusResult === undefined || ((onboardingStatusResult as any)?.authError && (retryCount < 2 || hasTriedCreatingUser))) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Sjekker bedriftsinformasjon...
            </h2>
            <p className="text-jobblogg-text-muted">
              {hasTriedCreatingUser ? 'Oppretter brukeroppføring...' :
               retryCount > 0 ? `Prøver igjen (${retryCount}/2)...` :
               'Vent litt mens vi henter dine data'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If contractor onboarding is not completed (or we couldn't determine status), redirect to onboarding
  console.log('🔍 Onboarding status check:', {
    onboardingStatus,
    contractorCompleted: onboardingStatus?.contractorCompleted,
    shouldRedirect: !onboardingStatus?.contractorCompleted,
    currentPath: location.pathname
  });

  if (!onboardingStatus?.contractorCompleted) {
    // Preserve the intended destination for redirect after onboarding
    const redirectTo = location.pathname !== '/contractor-onboarding' ? location.pathname : '/';
    const onboardingUrl = `/contractor-onboarding${redirectTo !== '/' ? `?redirect=${encodeURIComponent(redirectTo)}` : ''}`;

    console.log('🔄 Redirecting to onboarding:', onboardingUrl);
    return <Navigate to={onboardingUrl} replace />;
  }

  // Contractor onboarding is complete, render the protected content
  console.log('✅ Onboarding complete, rendering protected content');
  return <>{children}</>;
};


