import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
// import { PrimaryButton, SecondaryButton } from '../ui'; // Unused
import { formatTimeRemaining } from '../../utils/timeFormatting';

interface TrialExpiredBannerProps {
  onDismiss?: () => void;
  showDismiss?: boolean;
}

/**
 * Sticky top banner for trial expiration - non-invasive and always visible
 * Replaces the modal approach with a more professional banner design
 */
export const TrialExpiredBanner: React.FC<TrialExpiredBannerProps> = ({
  onDismiss,
  showDismiss = false
}) => {
  const { user } = useUser();
  const { subscription, isTrialExpired, isInGracePeriod } = useSubscriptionAccess();
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  // Don't show if not expired, no subscription, or dismissed
  if (!subscription || (!isTrialExpired && !isInGracePeriod) || isDismissed) return null;

  const handleUpgrade = async () => {
    if (!user) return;

    setIsUpgrading(true);
    try {
      // TODO: Re-enable when type instantiation issue is resolved
      console.log("⚠️ Create portal session temporarily disabled due to type issues");
      // const { url } = await createPortalSession({
      //   userId: user.id,
      //   returnUrl: window.location.href
      // });
      // window.location.href = url;
    } catch (error) {
      console.error('Failed to create portal session:', error);
      setIsUpgrading(false);
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  const getGracePeriodTimeLeft = () => {
    if (!(subscription as any)?.trialEnd) return { text: '0 dager', isUrgent: true };
    const gracePeriodEnd = (subscription as any).trialEnd + (3 * 24 * 60 * 60 * 1000); // 3 days after trial end
    return formatTimeRemaining(gracePeriodEnd);
  };

  const graceTimeLeft = getGracePeriodTimeLeft();

  return (
    <div className="sticky top-0 z-40 bg-gradient-to-r from-jobblogg-warning to-jobblogg-warning/90 border-b border-jobblogg-warning/20 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-3">
          {/* Left side - Icon and message */}
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium text-white">
                {isInGracePeriod ? 'Prøveperioden er utløpt' : 'Prøveperioden har utløpt'}
              </p>
              <p className="text-xs text-white/80">
                {isInGracePeriod ? (
                  <>Du har {graceTimeLeft.text} med begrenset tilgang</>
                ) : (
                  'Oppgrader for å fortsette å bruke alle funksjoner'
                )}
              </p>
            </div>
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-3">
            <button
              onClick={handleUpgrade}
              disabled={isUpgrading}
              className="bg-white text-amber-700 hover:bg-gray-50 active:bg-gray-100 disabled:opacity-50 font-semibold text-sm px-6 py-2 rounded-lg transition-all duration-200 min-h-[44px] min-w-[44px] shadow-sm hover:shadow-md touch-manipulation"
            >
              <span className="whitespace-nowrap">
                {isUpgrading ? 'Laster...' : 'Oppgrader nå'}
              </span>
            </button>
            
            {showDismiss && (
              <button
                onClick={handleDismiss}
                className="text-white/80 hover:text-white active:text-white/60 transition-colors p-2 min-h-[44px] min-w-[44px] flex items-center justify-center touch-manipulation"
                aria-label="Lukk varsel"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
