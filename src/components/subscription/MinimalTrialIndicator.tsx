import React, { useState, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { useRoleChangeListener } from '../../hooks/useRoleChangeHandler';
import { formatTimeRemaining, getDaysLeft } from '../../utils/timeFormatting';

export const MinimalTrialIndicator: React.FC = () => {
  const { subscription, isInTrial, isTrialExpired, isLoading } = useSubscriptionAccess();
  const { isAdministrator, isLoading: roleLoading } = useUserRole();
  const [_forceUpdate, setForceUpdate] = useState(0);

  // Listen for role changes and force re-render when user becomes administrator
  const handleRoleChange = useCallback((event: CustomEvent) => {
    if (event.detail.becameAdmin) {
      console.log('MinimalTrialIndicator: User became administrator, forcing update');
      setForceUpdate(prev => prev + 1);
    }
  }, []);

  useRoleChangeListener(handleRoleChange);

  // Don't show if loading or no subscription
  if (isLoading || roleLoading || !subscription) return null;

  // Show for trial users OR if trial has expired but we haven't reached the exact expiration time
  // After exact expiration, only show if isTrialExpired (for post-expiration states)
  const trialEnd = (subscription as any)?.trialEnd || 0;
  const isStillCountingDown = Date.now() < trialEnd;

  if (!isInTrial && !isTrialExpired && !isStillCountingDown) return null;

  const timeRemaining = formatTimeRemaining(trialEnd);
  const daysLeft = getDaysLeft(trialEnd); // For backward compatibility
  const isExpiringSoon = timeRemaining.isUrgent || daysLeft <= 2;

  // Trial expired state
  if (isTrialExpired) {
    if (!isAdministrator) {
      // Read-only information for non-administrators
      return (
        <div className="p-3 rounded-lg border border-jobblogg-border bg-jobblogg-neutral-soft">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-jobblogg-text-muted rounded-full"></div>
            <span className="text-sm font-medium text-jobblogg-text-muted">
              Prøveperioden er utløpt
            </span>
          </div>
        </div>
      );
    }

    // Administrator view with link to subscription management
    return (
      <Link
        to="/subscription"
        className="block p-3 rounded-lg border border-jobblogg-error bg-jobblogg-error-soft hover:bg-jobblogg-error-soft/80 transition-colors duration-200"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-jobblogg-error rounded-full"></div>
            <span className="text-sm font-medium text-jobblogg-error">
              Prøveperioden er utløpt
            </span>
          </div>
          <svg className="w-4 h-4 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </Link>
    );
  }

  // Active trial state - only show countdown if still counting down
  if (!isAdministrator) {
    // If trial has expired but we're still counting down, show countdown
    // If trial has actually expired, don't show (TrialExpiredBanner handles it)
    if (!isStillCountingDown && !isTrialExpired) return null;
    if (!isStillCountingDown && isTrialExpired) {
      // Show expired state for non-administrators
      return (
        <div className="p-3 rounded-lg border border-jobblogg-border bg-jobblogg-neutral-soft">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-jobblogg-text-muted rounded-full"></div>
            <span className="text-sm font-medium text-jobblogg-text-muted">
              Prøveperioden er utløpt
            </span>
          </div>
        </div>
      );
    }

    // Show countdown for active trial
    return (
      <div className={`p-3 rounded-lg border ${
        isExpiringSoon
          ? 'border-jobblogg-warning bg-jobblogg-warning-soft'
          : 'border-jobblogg-primary bg-jobblogg-primary-soft'
      }`}>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${
            isExpiringSoon ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
          }`}></div>
          <span className={`text-sm font-medium ${
            isExpiringSoon ? 'text-jobblogg-warning' : 'text-jobblogg-primary'
          }`}>
            {`Din bedrift har startet prøveabonnement: ${timeRemaining.text}`}
          </span>
        </div>
      </div>
    );
  }

  // Administrator view with link to subscription management
  // If trial has expired but we're still counting down, show countdown
  // If trial has actually expired, don't show countdown (TrialExpiredBanner handles it)
  if (!isStillCountingDown && !isTrialExpired) return null;
  if (!isStillCountingDown && isTrialExpired) {
    // Show expired state for administrators
    return (
      <Link
        to="/subscription"
        className="block p-3 rounded-lg border border-jobblogg-error bg-jobblogg-error-soft hover:bg-jobblogg-error-soft/80 transition-colors duration-200"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-jobblogg-error rounded-full"></div>
            <span className="text-sm font-medium text-jobblogg-error">
              Prøveperioden er utløpt
            </span>
          </div>
          <svg className="w-4 h-4 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </Link>
    );
  }

  // Show countdown for active trial
  return (
    <Link
      to="/subscription"
      className={`block p-3 rounded-lg border transition-colors duration-200 ${
        isExpiringSoon
          ? 'border-jobblogg-warning bg-jobblogg-warning-soft hover:bg-jobblogg-warning-soft/80'
          : 'border-jobblogg-primary bg-jobblogg-primary-soft hover:bg-jobblogg-primary-soft/80'
      }`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${
            isExpiringSoon ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
          }`}></div>
          <span className={`text-sm font-medium ${
            isExpiringSoon ? 'text-jobblogg-warning' : 'text-jobblogg-primary'
          }`}>
            {`Prøveperiode: ${timeRemaining.text}`}
          </span>
        </div>
        <svg className={`w-4 h-4 ${
          isExpiringSoon ? 'text-jobblogg-warning' : 'text-jobblogg-primary'
        }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </div>
    </Link>
  );
};
