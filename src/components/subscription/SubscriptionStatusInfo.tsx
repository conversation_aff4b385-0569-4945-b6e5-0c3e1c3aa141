import React, { useState, useCallback } from 'react';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { useRoleChangeListener } from '../../hooks/useRoleChangeHandler';

/**
 * Read-only subscription status information component for non-administrators
 * Shows subscription status without management controls
 */
export const SubscriptionStatusInfo: React.FC = () => {
  const { subscription, isInTrial, isTrialExpired, isLoading } = useSubscriptionAccess();
  const { isAdministrator, isLoading: roleLoading } = useUserRole();
  const [forceUpdate, setForceUpdate] = useState(0);

  // Listen for role changes and hide component when user becomes administrator
  const handleRoleChange = useCallback((event: CustomEvent) => {
    if (event.detail.becameAdmin || event.detail.lostAdmin) {
      console.log('SubscriptionStatusInfo: Role change detected, updating visibility');
      setForceUpdate(prev => prev + 1);
    }
  }, []);

  useRoleChangeListener(handleRoleChange);

  // Don't show if loading or no subscription
  if (isLoading || roleLoading || !subscription) return null;

  // Only show for non-administrators
  if (isAdministrator) return null;

  // Don't show for active paid subscriptions
  if (!isInTrial && !isTrialExpired) return null;

  const now = Date.now();
  const trialEnd = subscription.trialEnd || 0;
  const daysLeft = Math.max(0, Math.ceil((trialEnd - now) / (24 * 60 * 60 * 1000)));

  // Trial expired state
  if (isTrialExpired) {
    return (
      <div className="p-4 rounded-lg border border-jobblogg-border bg-jobblogg-neutral-soft">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-jobblogg-text-muted/20 flex items-center justify-center">
            <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-medium text-jobblogg-text-strong">
              Prøveperioden er utløpt
            </h4>
            <p className="text-sm text-jobblogg-text-medium">
              Bedriften har ikke oppgradert til betalt abonnement. Kontakt administrator for å oppgradere.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Active trial state
  return (
    <div className="p-4 rounded-lg border border-jobblogg-primary bg-jobblogg-primary-soft">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 rounded-full bg-jobblogg-primary flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div>
          <h4 className="font-medium text-jobblogg-text-strong">
            Din bedrift har startet prøveabonnement
          </h4>
          <p className="text-sm text-jobblogg-text-medium">
            {daysLeft > 0 
              ? `${daysLeft} ${daysLeft === 1 ? 'dag' : 'dager'} igjen av prøveperioden`
              : 'Prøveperioden utløper i dag'
            }
          </p>
        </div>
      </div>
    </div>
  );
};
