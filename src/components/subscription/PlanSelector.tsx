import React, { useState, useEffect } from 'react';
import { Heading2, BodyText, TextMuted, PrimaryButton } from '../ui';

interface PlanTier {
  id: 'basic' | 'professional' | 'enterprise';
  name: string;
  description: string;
  employeeRange: string;
  monthlyPrice: number;
  annualPrice: number;
  monthlyPriceExclVat: number;
  annualPriceExclVat: number;
  annualEquivalentMonthly: number;
  annualEquivalentMonthlyExclVat: number;
  features: string[];
  isRecommended?: boolean;
}

interface PlanSelectorProps {
  currentPlan?: 'basic' | 'professional' | 'enterprise';
  currentBilling?: 'month' | 'year';
  onPlanSelect: (planLevel: string, billingInterval: 'month' | 'year') => void;
  onConfirm?: () => void;
  title?: string;
  subtitle?: string;
  showConfirmButton?: boolean;
  isTrialMode?: boolean;
}

const pricingTiers: PlanTier[] = [
  {
    id: 'basic',
    name: 'Liten bedrift',
    description: 'Perfekt for enkeltmannsforetak og små team',
    employeeRange: '1–9 ansatte',
    monthlyPrice: 299,
    annualPrice: 239 * 12,
    monthlyPriceExclVat: 299,
    annualPriceExclVat: 239 * 12,
    annualEquivalentMonthly: 239,
    annualEquivalentMonthlyExclVat: 239,
    features: [
      'Ubegrenset prosjekter',
      'Prosjektlogg med bilder',
      'Chat med kunder og team',
      'Prosjektdeling med kunder',
      'Prosjektdeling med underleverandører',
      'Mobil app (iOS/Android)',
      '7 dagers gratis prøveperiode',
    ]
  },
  {
    id: 'professional',
    name: 'Mellomstor bedrift',
    description: 'For voksende entreprenørfirmaer',
    employeeRange: '10–49 ansatte',
    monthlyPrice: 999,
    annualPrice: 799 * 12,
    monthlyPriceExclVat: 999,
    annualPriceExclVat: 799 * 12,
    annualEquivalentMonthly: 799,
    annualEquivalentMonthlyExclVat: 799,
    features: [
      'Ubegrenset prosjekter',
      'Prosjektlogg med bilder',
      'Chat med kunder og team',
      'Prosjektdeling med kunder',
      'Prosjektdeling med underleverandører',
      'Mobil app (iOS/Android)',
      '7 dagers gratis prøveperiode',
    ]
  },
  {
    id: 'enterprise',
    name: 'Stor bedrift',
    description: 'For etablerte byggefirmaer',
    employeeRange: '50–249 ansatte',
    monthlyPrice: 2999,
    annualPrice: 2399 * 12,
    monthlyPriceExclVat: 2999,
    annualPriceExclVat: 2399 * 12,
    annualEquivalentMonthly: 2399,
    annualEquivalentMonthlyExclVat: 2399,
    features: [
      'Ubegrenset prosjekter',
      'Prosjektlogg med bilder',
      'Chat med kunder og team',
      'Prosjektdeling med kunder',
      'Prosjektdeling med underleverandører',
      'Mobil app (iOS/Android)',
      '7 dagers gratis prøveperiode',
    ]
  }
];

const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('nb-NO').format(price);
};

export const PlanSelector: React.FC<PlanSelectorProps> = ({
  currentPlan = 'professional',
  currentBilling = 'month',
  onPlanSelect,
  onConfirm,
  title = 'Velg din plan',
  subtitle = 'Velg planen som passer best for din bedrift',
  showConfirmButton = false,
  isTrialMode = false,
}) => {
  const [selectedPlan, setSelectedPlan] = useState<'basic' | 'professional' | 'enterprise'>(currentPlan);
  const [billingInterval, setBillingInterval] = useState<'month' | 'year'>(currentBilling);

  useEffect(() => {
    setSelectedPlan(currentPlan);
    setBillingInterval(currentBilling);
  }, [currentPlan, currentBilling]);

  const handlePlanSelect = (planId: 'basic' | 'professional' | 'enterprise') => {
    setSelectedPlan(planId);
    onPlanSelect(planId, billingInterval);
  };

  const handleBillingChange = (interval: 'month' | 'year') => {
    setBillingInterval(interval);
    onPlanSelect(selectedPlan, interval);
  };

  const getPriceDisplay = (tier: PlanTier) => {
    if (billingInterval === 'year') {
      return {
        primary: `${formatPrice(tier.annualPriceExclVat)} kr/år`,
        secondary: `tilsvarer ${formatPrice(tier.annualEquivalentMonthlyExclVat)} kr/mnd`
      };
    } else {
      return {
        primary: `${formatPrice(tier.monthlyPriceExclVat)} kr/mnd`,
        secondary: null
      };
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <Heading2>{title}</Heading2>
        <BodyText className="text-jobblogg-text-muted mt-2">
          {subtitle}
        </BodyText>
      </div>

      {/* Billing Toggle */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-4 p-1 bg-jobblogg-surface rounded-xl border border-jobblogg-border">
          <button
            onClick={() => handleBillingChange('month')}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
              billingInterval === 'month'
                ? 'bg-white text-jobblogg-primary shadow-sm'
                : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
            }`}
          >
            Betal Månedlig
          </button>
          <button
            onClick={() => handleBillingChange('year')}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
              billingInterval === 'year'
                ? 'bg-white text-jobblogg-primary shadow-sm'
                : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
            }`}
          >
            Betal Årlig (Spar 20%)
          </button>
        </div>
      </div>

      {/* Plan Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {pricingTiers.map((tier) => {
          const isSelected = selectedPlan === tier.id;
          const isCurrentPlan = currentPlan === tier.id;
          const priceDisplay = getPriceDisplay(tier);

          return (
            <div
              key={tier.id}
              className={`relative bg-white rounded-xl border-2 p-6 cursor-pointer transition-all duration-300 ${
                isSelected
                  ? 'border-jobblogg-primary shadow-lg'
                  : 'border-jobblogg-border hover:border-jobblogg-primary/50 hover:shadow-md'
              }`}
              onClick={() => handlePlanSelect(tier.id)}
            >
              {/* Current Plan Badge */}
              {isCurrentPlan && !isTrialMode && (
                <div className="absolute -top-3 left-6">
                  <span className="bg-jobblogg-success text-white px-3 py-1 rounded-full text-xs font-medium">
                    Nåværende plan
                  </span>
                </div>
              )}

              <div className="space-y-4">
                {/* Plan Header */}
                <div>
                  <h3 className="text-xl font-bold text-jobblogg-text-strong mb-1">{tier.name}</h3>
                  <p className="text-sm text-jobblogg-text-muted">{tier.employeeRange}</p>
                  <p className="text-sm text-jobblogg-text-medium mt-2">{tier.description}</p>
                </div>

                {/* Pricing */}
                <div className="py-4">
                  <div className="flex items-baseline gap-1">
                    <span className="text-4xl font-bold text-jobblogg-text-strong">
                      {priceDisplay.primary}
                    </span>
                  </div>
                  <p className="text-sm text-jobblogg-text-muted mt-1">
                    ekskl. mva.
                  </p>

                  {priceDisplay.secondary && (
                    <p className="text-sm text-jobblogg-text-medium mt-1">
                      {priceDisplay.secondary}
                    </p>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-2">
                  {tier.features.slice(0, 6).map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-sm text-jobblogg-text-medium">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Selection Indicator */}
                <div className="pt-4">
                  <div
                    className={`w-full py-3 px-4 rounded-xl font-semibold text-center transition-all duration-200 ${
                      isSelected
                        ? 'bg-jobblogg-primary text-white shadow-md'
                        : 'border-2 border-jobblogg-border text-jobblogg-text-medium'
                    }`}
                  >
                    {isSelected ? 'Valgt' : 'Velg denne planen'}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Confirm Button */}
      {showConfirmButton && onConfirm && (
        <div className="flex justify-center">
          <PrimaryButton onClick={onConfirm} className="px-8">
            Bekreft planvalg
          </PrimaryButton>
        </div>
      )}

      {/* Trial Information */}
      {isTrialMode && (
        <div className="bg-jobblogg-primary-soft rounded-lg p-4 border border-jobblogg-primary/20">
          <div className="flex items-center gap-2 mb-2">
            <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm font-medium text-jobblogg-primary">
              Planendring under prøveperiode
            </span>
          </div>
          <p className="text-sm text-jobblogg-primary">
            Du kan endre plan når som helst under prøveperioden. Prøveperioden din fortsetter uavbrutt.
          </p>
        </div>
      )}
    </div>
  );
};
