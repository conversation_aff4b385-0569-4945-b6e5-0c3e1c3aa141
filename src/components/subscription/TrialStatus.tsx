import React, { useState, useCallback } from 'react';
// import { useAction } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { useRoleChangeListener } from '../../hooks/useRoleChangeHandler';
import { PrimaryButton } from '../ui/Button';
import { formatTimeRemaining, getDaysLeft } from '../../utils/timeFormatting';

export const TrialStatus: React.FC = () => {
  const { user } = useUser();
  const { subscription, isInTrial, isTrialExpired, isLoading } = useSubscriptionAccess();
  const { isAdministrator, isLoading: roleLoading } = useUserRole();
  // TODO: Re-enable when type instantiation issue is resolved
  // const createPortalSession = useAction(api.subscriptions.createPortalSession as any);
  const createPortalSession = async (args: any) => {
    console.log("⚠️ Create portal session temporarily disabled due to type issues", args);
    return { url: '#' };
  }; // Temporarily mock action
  const [_forceUpdate, setForceUpdate] = useState(0);

  // Listen for role changes and force re-render when user becomes administrator
  const handleRoleChange = useCallback((event: CustomEvent) => {
    if (event.detail.becameAdmin) {
      console.log('TrialStatus: User became administrator, forcing update');
      setForceUpdate(prev => prev + 1);
    }
  }, []);

  useRoleChangeListener(handleRoleChange);

  // Don't show if loading or no subscription
  if (isLoading || roleLoading || !subscription) return null;

  // Show for trial users OR if trial has expired but we haven't reached the exact expiration time
  // This ensures we show countdown until the very last minute, then hide to avoid duplicate with banner
  const trialEnd = (subscription as any)?.trialEnd || 0;
  const isStillCountingDown = Date.now() < trialEnd;

  if (!isInTrial && !isStillCountingDown) return null;

  const timeRemaining = formatTimeRemaining(trialEnd);
  const daysLeft = getDaysLeft(trialEnd); // For backward compatibility with existing logic
  const isExpiringSoon = timeRemaining.isUrgent || daysLeft <= 2;
  const isNewTrial = daysLeft >= 6; // First day or two of trial

  const handleManageSubscription = async () => {
    if (!user) return;
    
    try {
      const { url } = await createPortalSession({
        userId: user.id,
        returnUrl: window.location.href
      });
      window.location.href = url;
    } catch (error) {
      console.error('Failed to create portal session:', error);
    }
  };

  // Trial expired state
  if (isTrialExpired) {
    if (!isAdministrator) {
      // Read-only information for non-administrators
      return (
        <div className="p-6 rounded-xl border bg-jobblogg-neutral-soft border-jobblogg-border">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-jobblogg-text-muted/20 flex items-center justify-center">
              <svg className="w-5 h-5 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-1">
                Prøveperioden er utløpt
              </h3>
              <p className="text-jobblogg-text-medium">
                Bedriften har ikke oppgradert til betalt abonnement. Kontakt administrator for å oppgradere.
              </p>
            </div>
          </div>
        </div>
      );
    }

    // Administrator view with action button
    return (
      <div className="p-6 rounded-xl border bg-jobblogg-error-soft border-jobblogg-error">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
              Prøveperioden er utløpt
            </h3>
            <p className="text-jobblogg-text-medium">
              Oppgrader nå for å fortsette å bruke alle funksjoner i JobbLogg
            </p>
          </div>
          <PrimaryButton onClick={handleManageSubscription}>
            Oppgrader nå
          </PrimaryButton>
        </div>
      </div>
    );
  }

  // Active trial state - enhanced UX
  return (
    <div
      data-trial-status
      className={`p-6 rounded-xl border ${
        isExpiringSoon
          ? 'bg-jobblogg-warning-soft border-jobblogg-warning'
          : 'bg-jobblogg-primary-soft border-jobblogg-primary'
      }`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-3">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              isExpiringSoon ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
            }`}>
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-jobblogg-text-strong">
                {isNewTrial
                  ? `Prøveperioden er aktiv! ${timeRemaining.text}`
                  : timeRemaining.text === 'Prøveperioden er utløpt'
                  ? timeRemaining.text
                  : `${timeRemaining.text} av prøveperioden`
                }
              </h3>
              <p className="text-sm text-jobblogg-text-medium">
                {isNewTrial
                  ? 'Utforsk alle funksjoner uten begrensninger'
                  : isExpiringSoon
                  ? 'Oppgrader nå for å unngå avbrudd i tjenesten'
                  : 'Velg en plan som passer for din bedrift'
                }
              </p>
            </div>
          </div>

          {/* Progress bar */}
          <div className="mb-4">
            <div className="flex justify-between text-xs text-jobblogg-text-medium mb-1">
              <span>Prøveperiode</span>
              <span>{7 - daysLeft}/7 dager brukt</span>
            </div>
            <div className="w-full bg-white/50 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  isExpiringSoon ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
                }`}
                style={{ width: `${Math.min(100, ((7 - daysLeft) / 7) * 100)}%` }}
              />
            </div>
          </div>

          {/* Current plan info */}
          <div className="bg-white/30 rounded-lg p-3 mb-4">
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm font-medium text-jobblogg-text-strong">
                  Nåværende plan: {(subscription as any)?.planLevel === 'basic' ? 'Liten bedrift' :
                                   (subscription as any)?.planLevel === 'professional' ? 'Mellomstor bedrift' : 'Stor bedrift'}
                </span>
                <p className="text-xs text-jobblogg-text-medium">
                  {(subscription as any)?.billingInterval === 'year' ? 'Årlig fakturering' : 'Månedlig fakturering'}
                </p>
              </div>
              <div className="text-right">
                <span className="text-sm font-bold text-jobblogg-text-strong">
                  Gratis
                </span>
                <p className="text-xs text-jobblogg-text-medium">
                  i {timeRemaining.shortText} til
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action buttons - only for administrators */}
        {isAdministrator && (
          <div className="ml-6 flex flex-col gap-2">
            <PrimaryButton
              onClick={handleManageSubscription}
              className="min-h-[44px] whitespace-nowrap"
            >
              {isExpiringSoon ? 'Oppgrader nå' : isNewTrial ? 'Administrer plan' : 'Velg abonnement'}
            </PrimaryButton>
            {!isExpiringSoon && (
              <button
                onClick={() => {
                  // Simple dismiss functionality - could be enhanced with localStorage to remember dismissal
                  const element = document.querySelector('[data-trial-status]') as HTMLElement;
                  if (element) {
                    element.style.display = 'none';
                  }
                }}
                className="text-xs text-jobblogg-text-muted hover:text-jobblogg-text-medium transition-colors"
              >
                Påminn meg senere
              </button>
            )}
          </div>
        )}

        {/* Information-only for non-administrators */}
        {!isAdministrator && (
          <div className="ml-6 flex items-center">
            <div className="text-right">
              <div className="text-sm font-medium text-jobblogg-text-strong">
                Din bedrift har startet prøveabonnement
              </div>
              <div className="text-xs text-jobblogg-text-muted">
                Kontakt administrator for abonnementsendringer
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
