import React from 'react';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { UpgradePrompt } from './UpgradePrompt';
// import { TrialExpiredPrompt } from './TrialExpiredPrompt'; // Unused
import { DisabledFeature } from './DisabledFeature';
import { DisabledButton } from './DisabledButton';

interface SubscriptionGateProps {
  children: React.ReactNode;
  feature: 'create_project' | 'full_access' | 'view_projects' | 'read_only' | 'team_management' | 'project_sharing' | 'file_upload';
  fallback?: React.ReactNode;
  showTrialExpiredPrompt?: boolean;
  variant?: 'block' | 'disable' | 'button'; // How to handle restricted access
  className?: string;
}

export const SubscriptionGate: React.FC<SubscriptionGateProps> = ({
  children,
  feature,
  fallback,
  // showTrialExpiredPrompt = true, // Unused parameter
  variant = 'block',
  className = ''
}) => {
  const {
    canCreateProjects,
    canAccessProjects,
    hasFullAccess,
    isLoading,
    needsTrialSetup,
    isInTrial,
    isTrialExpired,
    isInGracePeriod,
    isReadOnly,
    subscription
  } = useSubscriptionAccess();
  const { isAdministrator: _isAdministrator, isLoading: roleLoading } = useUserRole();

  // Show loading state
  if (isLoading || roleLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary"></div>
      </div>
    );
  }

  // Check access based on feature
  const hasAccess = {
    create_project: canCreateProjects,
    full_access: hasFullAccess,
    view_projects: canAccessProjects,
    read_only: canAccessProjects || isReadOnly, // Allow read-only access for expired trials
    team_management: hasFullAccess, // Team management requires full access
    project_sharing: hasFullAccess, // Project sharing requires full access
    file_upload: hasFullAccess, // File uploads require full access
  }[feature];

  // For expired trials, show appropriate fallback with improved UX
  // The new TrialExpiredBanner is now handled globally in AuthenticatedLayout
  if ((isTrialExpired || isInGracePeriod) && feature !== 'read_only') {
    if (feature === 'view_projects' && isReadOnly) {
      // Allow read-only access - banner is shown globally
      return <>{children}</>;
    }

    // Handle restricted features with improved UX based on variant
    const restrictedFeatures = ['create_project', 'full_access', 'team_management', 'project_sharing', 'file_upload'];
    if (restrictedFeatures.includes(feature)) {
      const reason = isInGracePeriod ? 'grace_period' : 'trial_expired';

      if (variant === 'disable') {
        return (
          <DisabledFeature
            feature={feature as any}
            reason={reason}
            className={className}
          >
            {children}
          </DisabledFeature>
        );
      }

      if (variant === 'button') {
        return (
          <DisabledButton
            feature={feature as any}
            reason={reason}
            className={className}
          >
            {children}
          </DisabledButton>
        );
      }

      // Default block variant - fallback to custom or default message
      return fallback || (
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
            Funksjon ikke tilgjengelig
          </h3>
          <p className="text-jobblogg-text-medium">
            Oppgrader til en betalt plan for å få tilgang til denne funksjonen.
          </p>
        </div>
      );
    }
  }

  if (!hasAccess) {
    // Don't show upgrade prompt for users who have an active trial subscription
    // This prevents showing "Start trial" messages after onboarding completion
    if (isInTrial && subscription) {
      return null; // Hide the gate entirely for trial users - they should have access
    }

    // Pass role information to UpgradePrompt for role-based rendering
    return fallback || <UpgradePrompt feature={feature} needsTrialSetup={needsTrialSetup} />;
  }

  return <>{children}</>;
};
