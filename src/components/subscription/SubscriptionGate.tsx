import React from 'react';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { UpgradePrompt } from './UpgradePrompt';
import { TrialExpiredPrompt } from './TrialExpiredPrompt';

interface SubscriptionGateProps {
  children: React.ReactNode;
  feature: 'create_project' | 'full_access' | 'view_projects' | 'read_only';
  fallback?: React.ReactNode;
  showTrialExpiredPrompt?: boolean;
}

export const SubscriptionGate: React.FC<SubscriptionGateProps> = ({
  children,
  feature,
  fallback,
  showTrialExpiredPrompt = true
}) => {
  const {
    canCreateProjects,
    canAccessProjects,
    hasFullAccess,
    isLoading,
    needsTrialSetup,
    isInTrial,
    isTrialExpired,
    isInGracePeriod,
    isReadOnly,
    subscription
  } = useSubscriptionAccess();
  const { isAdministrator, isLoading: roleLoading } = useUserRole();

  // Show loading state
  if (isLoading || roleLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary"></div>
      </div>
    );
  }

  // Check access based on feature
  const hasAccess = {
    create_project: canCreateProjects,
    full_access: hasFullAccess,
    view_projects: canAccessProjects,
    read_only: canAccessProjects || isReadOnly, // Allow read-only access for expired trials
  }[feature];

  // Show trial expired prompt for expired trials (unless disabled)
  if ((isTrialExpired || isInGracePeriod) && showTrialExpiredPrompt && feature !== 'read_only') {
    if (feature === 'view_projects' && isReadOnly) {
      // Allow read-only access but show banner
      return (
        <>
          <TrialExpiredPrompt variant="banner" />
          {children}
        </>
      );
    }

    // Block access for create/full access features
    if (feature === 'create_project' || feature === 'full_access') {
      return fallback || <TrialExpiredPrompt variant="page" />;
    }
  }

  if (!hasAccess) {
    // Don't show upgrade prompt for users who have an active trial subscription
    // This prevents showing "Start trial" messages after onboarding completion
    if (isInTrial && subscription) {
      return null; // Hide the gate entirely for trial users - they should have access
    }

    // Pass role information to UpgradePrompt for role-based rendering
    return fallback || <UpgradePrompt feature={feature} needsTrialSetup={needsTrialSetup} />;
  }

  return <>{children}</>;
};
