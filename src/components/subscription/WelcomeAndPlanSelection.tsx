import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { useNavigate } from 'react-router-dom';
import { api } from '../../../convex/_generated/api';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { Heading2, BodyText, TextMuted, PrimaryButton, SecondaryButton, Card } from '../ui';

export const WelcomeAndPlanSelection: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const { subscription, isInTrial, needsTrialSetup, isLoading } = useSubscriptionAccess();
  const { isAdministrator } = useUserRole();
  const createTrialSubscription = useMutation(api.subscriptions.createTrialSubscription);
  const createPortalSession = useMutation(api.subscriptions.createPortalSession);
  const [isStartingTrial, setIsStartingTrial] = useState(false);

  // Don't show if loading or user already has subscription
  if (isLoading || !user) return null;

  // Only show for administrators
  if (!isAdministrator) return null;

  // Only show for users who need trial setup (no subscription at all)
  // Don't show for users who already have a trial subscription (from onboarding)
  if (!needsTrialSetup) return null;

  const handleStartTrial = async () => {
    if (!user) return;

    setIsStartingTrial(true);
    try {
      await createTrialSubscription({
        userId: user.id,
        email: user.emailAddresses[0]?.emailAddress || '',
        name: user.fullName || '',
      });
      
      // Refresh the page to update subscription status
      window.location.reload();
    } catch (error) {
      console.error('Failed to start trial:', error);
      setIsStartingTrial(false);
    }
  };

  const handleManageSubscription = async () => {
    if (!user) return;

    try {
      const { url } = await createPortalSession({
        userId: user.id,
        returnUrl: window.location.href
      });
      window.location.href = url;
    } catch (error) {
      console.error('Failed to create portal session:', error);
    }
  };

  const handleCreateFirstProject = () => {
    navigate('/create');
  };

  // Welcome message for new users who need trial setup
  if (needsTrialSetup) {
    return (
      <Card className="bg-gradient-to-r from-jobblogg-primary-soft to-jobblogg-accent-soft border-jobblogg-primary p-8">
        <div className="text-center space-y-6">
          <div className="w-16 h-16 bg-jobblogg-primary rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          
          <div>
            <Heading2 className="text-jobblogg-text-strong mb-2">
              Velkommen til JobbLogg! 🎉
            </Heading2>
            <BodyText className="text-jobblogg-text-medium">
              Du er nå klar til å starte din gratis 7-dagers prøveperiode. 
              Ingen kredittkort påkrevd - du kan utforske alle funksjoner uten forpliktelser.
            </BodyText>
          </div>

          <div className="bg-white/50 rounded-lg p-4 space-y-3">
            <h4 className="font-semibold text-jobblogg-text-strong">
              Hva kan du gjøre i prøveperioden?
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-jobblogg-success" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Opprett ubegrenset prosjekter</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-jobblogg-success" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Dokumenter med bilder</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-jobblogg-success" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Chat med kunder</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-jobblogg-success" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Inviter team medlemmer</span>
              </div>
            </div>
          </div>

          <PrimaryButton 
            onClick={handleStartTrial}
            disabled={isStartingTrial}
            className="min-h-[44px] px-8"
          >
            {isStartingTrial ? 'Starter prøveperiode...' : 'Start 7-dagers gratis prøveperiode'}
          </PrimaryButton>
        </div>
      </Card>
    );
  }

  // Welcome message for users in early trial
  if (isInTrial && subscription) {
    const now = Date.now();
    const trialEnd = subscription.trialEnd || 0;
    const daysLeft = Math.max(0, Math.ceil((trialEnd - now) / (24 * 60 * 60 * 1000)));
    const trialDaysUsed = Math.ceil((now - (subscription.trialStart || 0)) / (24 * 60 * 60 * 1000));
    
    // Only show for first 2 days of trial
    if (trialDaysUsed > 2) return null;

    return (
      <Card className="bg-gradient-to-r from-jobblogg-success-soft to-jobblogg-primary-soft border-jobblogg-success p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-8 h-8 bg-jobblogg-success rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <Heading2 className="text-jobblogg-text-strong">
                Prøveperioden er i gang! 
              </Heading2>
            </div>
            
            <BodyText className="text-jobblogg-text-medium mb-4">
              Du har {daysLeft} {daysLeft === 1 ? 'dag' : 'dager'} igjen av din gratis prøveperiode. 
              Utforsk alle funksjoner og se hvordan JobbLogg kan hjelpe deg med prosjektadministrasjon.
            </BodyText>

            <div className="flex flex-col sm:flex-row gap-3">
              <PrimaryButton 
                onClick={handleManageSubscription}
                className="min-h-[44px]"
              >
                Se abonnementsplaner
              </PrimaryButton>
              <SecondaryButton
                onClick={handleCreateFirstProject}
                className="min-h-[44px]"
              >
                Opprett ditt første prosjekt
              </SecondaryButton>
            </div>
          </div>

          <div className="ml-6 text-center">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-2">
              <span className="text-2xl font-bold text-jobblogg-text-strong">
                {daysLeft}
              </span>
            </div>
            <TextMuted className="text-xs">
              {daysLeft === 1 ? 'dag igjen' : 'dager igjen'}
            </TextMuted>
          </div>
        </div>
      </Card>
    );
  }

  return null;
};
