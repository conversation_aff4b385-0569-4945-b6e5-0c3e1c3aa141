import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  Modal,
  Heading2,
  Heading3,
  BodyText,
  TextMuted,
  PrimaryButton,
  SecondaryButton,
  DangerButton
} from '../ui';
import { RemoveTeamMemberModal } from './RemoveTeamMemberModal';
import { BlockTeamMemberModal } from './BlockTeamMemberModal';
import { UnblockTeamMemberModal } from './UnblockTeamMemberModal';
import { ChangeRoleModal } from './ChangeRoleModal';
import { useUserRole } from '../../hooks/useUserRole';

interface TeamMember {
  _id: string;
  clerkUserId: string;
  role: string;
  invitationStatus?: string;
  invitedAt?: number;
  acceptedAt?: number;
  createdAt: number;
  // Invitation data for display
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  // Activity data for "sist logget inn"
  lastLoginAt?: number;
  lastActivityAt?: number;
  // Blocking status
  isBlocked?: boolean;
  blockedAt?: number;
  blockedBy?: string;
  blockedReason?: string;
}

interface TeamMemberDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  member: TeamMember;
  onMemberUpdate?: () => void;
}

export const TeamMemberDetailsModal: React.FC<TeamMemberDetailsModalProps> = ({
  isOpen,
  onClose,
  member,
  onMemberUpdate,
}) => {
  const { user } = useUser();
  const { isAdministrator } = useUserRole();
  const [showRemoveModal, setShowRemoveModal] = useState(false);
  const [showBlockModal, setShowBlockModal] = useState(false);
  const [showUnblockModal, setShowUnblockModal] = useState(false);
  const [showChangeRoleModal, setShowChangeRoleModal] = useState(false);

  const isPendingInvitation = member.invitationStatus === 'pending' ||
    (!member.invitationStatus && !member.acceptedAt);

  const isActiveUser = member.invitationStatus === 'accepted' ||
    (!member.invitationStatus && member.acceptedAt);

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'administrator': return 'Administrator';
      case 'prosjektleder': return 'Prosjektleder';
      case 'utfoerende': return 'Utførende';
      default: return 'Utførende';
    }
  };

  // Format dates
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleRemoveSuccess = () => {
    onMemberUpdate?.();
    onClose();
  };

  const roleLabel = getRoleLabel(member.role);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-start gap-4">
          <div className="w-16 h-16 rounded-full bg-jobblogg-primary/10 flex items-center justify-center flex-shrink-0">
            <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          
          <div className="flex-1">
            <Heading2>
              {member.isBlocked
                ? 'Sperret Teammedlem'
                : isPendingInvitation
                ? 'Ventende Invitasjon'
                : 'Teammedlem Detaljer'
              }
            </Heading2>
            <TextMuted>
              {member.isBlocked
                ? 'Teammedlem er sperret fra tilgang'
                : isPendingInvitation
                ? 'Invitasjon sendt, venter på aksept'
                : 'Aktiv teammedlem'
              }
            </TextMuted>
          </div>
        </div>

        {/* Member Information */}
        <div className="space-y-4">
          {/* Contact Information */}
          {(member.firstName || member.lastName || member.email || member.phone) && (
            <div className="bg-jobblogg-neutral/30 rounded-lg p-4 space-y-3">
              <Heading3 className="text-sm font-semibold text-jobblogg-text-strong mb-3">
                Kontaktinformasjon
              </Heading3>

              {(member.firstName || member.lastName) && (
                <div className="flex items-center justify-between">
                  <BodyText className="font-medium">Navn</BodyText>
                  <TextMuted>
                    {`${member.firstName || ''} ${member.lastName || ''}`.trim()}
                  </TextMuted>
                </div>
              )}

              {member.email && (
                <div className="flex items-center justify-between">
                  <BodyText className="font-medium">E-post</BodyText>
                  <TextMuted>
                    {member.email}
                  </TextMuted>
                </div>
              )}

              {member.phone && (
                <div className="flex items-center justify-between">
                  <BodyText className="font-medium">Telefon</BodyText>
                  <TextMuted>
                    {member.phone}
                  </TextMuted>
                </div>
              )}
            </div>
          )}

          {/* System Information */}
          <div className="bg-jobblogg-neutral/30 rounded-lg p-4 space-y-3">
            <Heading3 className="text-sm font-semibold text-jobblogg-text-strong mb-3">
              Systeminformasjon
            </Heading3>

            <div className="flex items-center justify-between">
              <BodyText className="font-medium">Bruker ID</BodyText>
              <TextMuted className="font-mono text-sm">
                {member.clerkUserId.slice(-8)}
              </TextMuted>
            </div>
            
            <div className="flex items-center justify-between">
              <BodyText className="font-medium">Rolle</BodyText>
              <div className="flex items-center gap-2">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  member.role === 'administrator'
                    ? 'bg-jobblogg-primary/10 text-jobblogg-primary border border-jobblogg-primary/20'
                    : member.role === 'prosjektleder'
                    ? 'bg-jobblogg-success/10 text-jobblogg-success border border-jobblogg-success/20'
                    : 'bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20'
                }`}>
                  {roleLabel}
                </span>
                {isAdministrator && isActiveUser && (
                  <button
                    onClick={() => setShowChangeRoleModal(true)}
                    className="text-jobblogg-primary hover:text-jobblogg-primary-dark text-xs font-medium hover:underline transition-colors"
                    title="Endre rolle"
                  >
                    Endre
                  </button>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <BodyText className="font-medium">Status</BodyText>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                member.isBlocked
                  ? 'bg-jobblogg-error/10 text-jobblogg-error border border-jobblogg-error/20'
                  : isPendingInvitation
                  ? 'bg-jobblogg-warning/10 text-jobblogg-warning border border-jobblogg-warning/20'
                  : 'bg-jobblogg-success/10 text-jobblogg-success border border-jobblogg-success/20'
              }`}>
                {member.isBlocked ? 'Sperret' : isPendingInvitation ? 'Ventende' : 'Aktiv'}
              </span>
            </div>

            {/* Last activity info - prioritize lastActivityAt, then lastLoginAt, then acceptedAt */}
            {!isPendingInvitation && (
              <div className="flex items-center justify-between">
                <BodyText className="font-medium">Sist aktiv</BodyText>
                <TextMuted className="text-sm">
                  {member.lastActivityAt ? (
                    formatDate(member.lastActivityAt)
                  ) : member.lastLoginAt ? (
                    formatDate(member.lastLoginAt)
                  ) : member.acceptedAt ? (
                    formatDate(member.acceptedAt)
                  ) : (
                    'Aldri'
                  )}
                </TextMuted>
              </div>
            )}
          </div>

          {/* Blocking Information */}
          {member.isBlocked && member.blockedAt && (
            <div className="bg-jobblogg-error/5 border border-jobblogg-error/10 rounded-lg p-4 space-y-3">
              <Heading3 className="text-sm font-semibold text-jobblogg-error mb-3">
                Sperring informasjon (kun for administratorer)
              </Heading3>

              <div className="flex items-center justify-between">
                <BodyText className="font-medium">Sperret</BodyText>
                <TextMuted className="text-sm">
                  {formatDate(member.blockedAt)}
                </TextMuted>
              </div>

              {member.blockedReason && (
                <div className="bg-jobblogg-neutral/20 rounded p-3">
                  <BodyText className="font-medium text-xs mb-1">Administratornotat:</BodyText>
                  <TextMuted className="text-sm">
                    {member.blockedReason}
                  </TextMuted>
                </div>
              )}

              {!member.blockedReason && (
                <TextMuted className="text-sm italic">
                  Ingen spesifikk grunn registrert.
                </TextMuted>
              )}

              <div className="bg-jobblogg-primary/5 border border-jobblogg-primary/10 rounded p-3 mt-3">
                <div className="flex items-start gap-2">
                  <svg className="w-4 h-4 text-jobblogg-primary flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <TextMuted className="text-xs">
                    <strong>Merk:</strong> Sperring årsak vises ikke til den sperrede brukeren.
                  </TextMuted>
                </div>
              </div>
            </div>
          )}

          {/* Timeline */}
          <div>
            <Heading3>Tidslinje</Heading3>
            <div className="space-y-3">
              {member.invitedAt && (
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-jobblogg-primary/10 flex items-center justify-center flex-shrink-0 mt-1">
                    <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <BodyText className="font-medium">Invitasjon sendt</BodyText>
                    <TextMuted className="text-sm">
                      {formatDate(member.invitedAt)}
                    </TextMuted>
                  </div>
                </div>
              )}

              {member.acceptedAt && (
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-jobblogg-success/10 flex items-center justify-center flex-shrink-0 mt-1">
                    <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <BodyText className="font-medium">Invitasjon akseptert</BodyText>
                    <TextMuted className="text-sm">
                      {formatDate(member.acceptedAt)}
                    </TextMuted>
                  </div>
                </div>
              )}

              {!member.invitedAt && member.createdAt && (
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-jobblogg-primary/10 flex items-center justify-center flex-shrink-0 mt-1">
                    <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <div>
                    <BodyText className="font-medium">Opprettet som medlem</BodyText>
                    <TextMuted className="text-sm">
                      {formatDate(member.createdAt)}
                    </TextMuted>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-jobblogg-border">
          <div className="flex items-center gap-2">
            {/* Block/Unblock button - only for active members */}
            {!isPendingInvitation && (
              <>
                {member.isBlocked ? (
                  <PrimaryButton
                    onClick={() => setShowUnblockModal(true)}
                    size="sm"
                    variant="outline"
                  >
                    Åpne bruker
                  </PrimaryButton>
                ) : (
                  <DangerButton
                    onClick={() => setShowBlockModal(true)}
                    size="sm"
                    variant="outline"
                  >
                    Sperr bruker
                  </DangerButton>
                )}
              </>
            )}

            {/* Remove button */}
            <DangerButton
              onClick={() => setShowRemoveModal(true)}
              size="sm"
              variant="outline"
            >
              {isPendingInvitation ? 'Kanseller invitasjon' : 'Fjern fra team'}
            </DangerButton>
          </div>

          <SecondaryButton onClick={onClose}>
            Lukk
          </SecondaryButton>
        </div>
      </div>

      {/* Remove Team Member Confirmation Modal */}
      <RemoveTeamMemberModal
        isOpen={showRemoveModal}
        onClose={() => setShowRemoveModal(false)}
        member={member}
        onSuccess={handleRemoveSuccess}
      />

      {/* Block Team Member Confirmation Modal */}
      <BlockTeamMemberModal
        isOpen={showBlockModal}
        onClose={() => setShowBlockModal(false)}
        member={member}
        onSuccess={handleRemoveSuccess}
      />

      {/* Unblock Team Member Confirmation Modal */}
      <UnblockTeamMemberModal
        isOpen={showUnblockModal}
        onClose={() => setShowUnblockModal(false)}
        member={member}
        onSuccess={handleRemoveSuccess}
      />

      {/* Change Role Modal */}
      <ChangeRoleModal
        isOpen={showChangeRoleModal}
        onClose={() => setShowChangeRoleModal(false)}
        member={member}
        onSuccess={handleRemoveSuccess}
      />
    </Modal>
  );
};
