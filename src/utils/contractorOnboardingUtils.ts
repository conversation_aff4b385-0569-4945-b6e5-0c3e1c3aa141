import { useUser, useAuth } from '@clerk/clerk-react';
import { useConvexAuth } from 'convex/react';
// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved

/**
 * Helper function to mark contractor onboarding as completed
 * Call this after successful onboarding completion
 */
export const markContractorOnboardingCompleted = (userId: string) => {
  const storageKey = `jobblogg-contractor-completed-${userId}`;
  const timestampKey = `${storageKey}-timestamp`;

  localStorage.setItem(storageKey, 'true');
  localStorage.setItem(timestampKey, Date.now().toString());

  console.log(`✅ Marked contractor onboarding as completed for user ${userId} at ${new Date().toISOString()}`);
};

/**
 * Helper function to reset contractor onboarding status
 * Useful for testing or if user needs to redo onboarding
 */
export const resetContractorOnboardingStatus = (userId: string) => {
  const storageKey = `jobblogg-contractor-completed-${userId}`;
  localStorage.removeItem(storageKey);
  console.log(`Reset contractor onboarding status for user ${userId}`);
};

/**
 * Comprehensive cleanup function for contractor onboarding
 * Removes all localStorage keys related to contractor onboarding
 */
export const clearAllContractorOnboardingData = (userId: string) => {
  const keysToRemove = [
    'jobblogg-contractor-onboarding', // Main onboarding data
    `jobblogg-contractor-completed-${userId}`, // Completion status
    'jobblogg-contractor-onboarding-temp', // Temporary data
    'jobblogg-contractor-form-backup', // Form backup
    'jobblogg-contractor-step-data', // Step-specific data
    'jobblogg-contractor-brreg-cache', // Brønnøysundregisteret cache
  ];

  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
    console.log(`Removed localStorage key: ${key}`);
  });

  // Also clear any dynamic keys that might exist
  const allKeys = Object.keys(localStorage);
  const contractorKeys = allKeys.filter(key => 
    key.includes('jobblogg-contractor') || 
    key.includes('contractor-onboarding')
  );

  contractorKeys.forEach(key => {
    localStorage.removeItem(key);
    console.log(`Removed dynamic contractor key: ${key}`);
  });

  console.log(`Cleared all contractor onboarding data for user ${userId}`);
};

/**
 * Helper function to check if contractor onboarding is completed in localStorage
 * This is a fallback when database queries are not available
 */
export const isContractorOnboardingCompletedInStorage = (userId: string): boolean => {
  if (!userId) return false;
  
  const storageKey = `jobblogg-contractor-completed-${userId}`;
  const completed = localStorage.getItem(storageKey);
  return completed === 'true';
};

/**
 * Hook to get contractor onboarding status (enhanced version)
 * Uses database first, localStorage as fallback
 */
export const useContractorOnboardingStatusSimple = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();
  const { isAuthenticated, isLoading: isConvexAuthLoading } = useConvexAuth();

  const isReady = isClerkLoaded && isAuthLoaded && !isConvexAuthLoading && isSignedIn && isAuthenticated && user?.id;

  // Query database for onboarding status
  // TODO: Re-enable when type instantiation issue is resolved
  // const onboardingStatusResult = useQuery(
  //   api.contractorOnboardingSafe.getContractorOnboardingStatusSafe,
  //   isReady ? { clerkUserId: user.id } : "skip"
  // );
  const onboardingStatusResult = undefined; // Temporarily disabled due to type instantiation issues

  // Fallback to localStorage if database query fails or is loading
  const localStorageCompleted = user?.id ? isContractorOnboardingCompletedInStorage(user.id) : false;

  const isLoading = !isReady || onboardingStatusResult === undefined;
  const databaseCompleted = (onboardingStatusResult as any)?.contractorCompleted || false;
  const hasError = (onboardingStatusResult as any)?.authError || (onboardingStatusResult as any)?.error;

  // Use database result if available, otherwise fall back to localStorage
  const isCompleted = hasError ? localStorageCompleted : databaseCompleted;

  return {
    isLoading,
    isCompleted,
    exists: (onboardingStatusResult as any)?.exists || false,
    contractorCompanyId: (onboardingStatusResult as any)?.contractorCompanyId || null,
    authError: (onboardingStatusResult as any)?.authError || false,
    error: (onboardingStatusResult as any)?.error || null,
    usingFallback: !!hasError,
  };
};

/**
 * Hook to check if we should show onboarding
 * Useful for conditional rendering in components
 * Uses database first, localStorage as fallback
 */
export const useShouldShowOnboarding = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();
  const { isAuthenticated, isLoading: isConvexAuthLoading } = useConvexAuth();

  const isReady = isClerkLoaded && isAuthLoaded && !isConvexAuthLoading && isSignedIn && isAuthenticated && user?.id;

  const onboardingStatus = useContractorOnboardingStatusSimple();

  return {
    isLoading: !isReady || onboardingStatus.isLoading,
    shouldShow: isReady && !onboardingStatus.isCompleted,
    isCompleted: onboardingStatus.isCompleted,
    error: onboardingStatus.error,
  };
};

/**
 * Hook to get contractor company data
 * Uses database query to fetch contractor company information
 */
export const useContractorCompanySimple = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();
  const { isAuthenticated, isLoading: isConvexAuthLoading } = useConvexAuth();

  const isReady = isClerkLoaded && isAuthLoaded && !isConvexAuthLoading && isSignedIn && isAuthenticated && user?.id;

  // TODO: Re-enable when type instantiation issue is resolved
  // const contractorCompanyResult = useQuery(
  //   api.contractorOnboardingSafe.getContractorCompanyByUserIdSafe,
  //   isReady ? { clerkUserId: user.id } : "skip"
  // );
  const contractorCompanyResult = undefined; // Temporarily disabled due to type instantiation issues

  return {
    isLoading: !isReady || contractorCompanyResult === undefined,
    company: (contractorCompanyResult as any)?.company || null,
    authError: (contractorCompanyResult as any)?.authError || false,
    error: (contractorCompanyResult as any)?.error || null,
  };
};
