/**
 * GDPR-Compliant Offline Storage Utility for JobbLogg PWA
 * Provides encrypted offline data storage and synchronization capabilities
 */

import { offlineEncryption, gdprCompliance, EncryptedData } from './offlineEncryption';

// Types for offline data
export interface OfflineProject {
  id: string;
  _id?: string;
  title?: string;
  name?: string;
  description?: string;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
  _creationTime?: number;
  userId?: string;
  customer?: any;
  isOffline?: boolean;
  syncStatus?: 'pending' | 'syncing' | 'synced' | 'error' | 'cached';
  cachedAt?: string;
}

export interface OfflineProjectLog {
  id: string;
  projectId: string;
  description: string;
  images: string[];
  createdAt: string;
  isOffline?: boolean;
  syncStatus?: 'pending' | 'syncing' | 'synced' | 'error';
}

export interface OfflineData {
  projects: OfflineProject[];
  projectLogs: OfflineProjectLog[];
  lastSync: string;
}

export interface SecureOfflineData {
  encrypted: EncryptedData;
  userId: string;
  consentTimestamp: number;
}

// Storage keys
const STORAGE_KEYS = {
  OFFLINE_DATA: 'jobblogg-offline-data-encrypted',
  SYNC_QUEUE: 'jobblogg-sync-queue-encrypted',
  LAST_SYNC: 'jobblogg-last-sync',
  LEGACY_DATA: 'jobblogg-offline-data', // For migration
  LEGACY_QUEUE: 'jobblogg-sync-queue'   // For migration
} as const;

/**
 * GDPR-Compliant Offline Storage Manager with Encryption
 */
export class OfflineStorageManager {
  private static instance: OfflineStorageManager;
  private syncQueue: Array<{ type: string; data: any; timestamp: string }> = [];
  private currentUserId: string | null = null;

  private constructor() {
    // Don't load sync queue immediately - wait for user authentication
  }

  static getInstance(): OfflineStorageManager {
    if (!OfflineStorageManager.instance) {
      OfflineStorageManager.instance = new OfflineStorageManager();
    }
    return OfflineStorageManager.instance;
  }

  /**
   * Initialize offline storage for a specific user with GDPR compliance
   */
  async initializeForUser(userId: string, sessionToken?: string): Promise<boolean> {
    try {
      console.log('[OfflineStorage] Initializing for user:', userId.substring(0, 8) + '...');

      // Check GDPR consent
      const hasConsent = gdprCompliance.hasValidConsent(userId);
      console.log('[OfflineStorage] GDPR consent check result:', hasConsent);

      if (!hasConsent) {
        console.log('[OfflineStorage] No valid GDPR consent for offline storage');
        return false;
      }

      // Initialize encryption
      console.log('[OfflineStorage] Initializing encryption...');
      await offlineEncryption.initializeEncryption(userId, sessionToken);
      this.currentUserId = userId;

      // Load sync queue after encryption is ready
      console.log('[OfflineStorage] Loading sync queue...');
      await this.loadSyncQueue();

      // Migrate legacy unencrypted data if exists
      console.log('[OfflineStorage] Migrating legacy data...');
      await this.migrateLegacyData();

      console.log('[OfflineStorage] ✅ Successfully initialized for user:', userId.substring(0, 8) + '...');
      return true;
    } catch (error) {
      console.error('[OfflineStorage] ❌ Failed to initialize:', error);
      return false;
    }
  }

  // Get offline data (encrypted)
  async getOfflineData(): Promise<OfflineData> {
    if (!this.currentUserId || !offlineEncryption.isInitialized()) {
      console.warn('[OfflineStorage] Cannot access offline data - not initialized');
      return { projects: [], projectLogs: [], lastSync: '' };
    }

    try {
      const encryptedDataStr = localStorage.getItem(STORAGE_KEYS.OFFLINE_DATA);
      if (!encryptedDataStr) {
        return { projects: [], projectLogs: [], lastSync: '' };
      }

      const secureData: SecureOfflineData = JSON.parse(encryptedDataStr);

      // Verify data belongs to current user
      if (secureData.userId !== this.currentUserId) {
        console.warn('[OfflineStorage] Offline data belongs to different user');
        return { projects: [], projectLogs: [], lastSync: '' };
      }

      const decryptedData = await offlineEncryption.decryptData<OfflineData>(secureData.encrypted);
      return decryptedData;
    } catch (error) {
      console.error('[OfflineStorage] Error loading encrypted offline data:', error);
      return { projects: [], projectLogs: [], lastSync: '' };
    }
  }

  // Save offline data (encrypted)
  async saveOfflineData(data: OfflineData): Promise<void> {
    if (!this.currentUserId || !offlineEncryption.isInitialized()) {
      console.warn('[OfflineStorage] Cannot save offline data - not initialized');
      return;
    }

    try {
      const encryptedData = await offlineEncryption.encryptData(data);
      const secureData: SecureOfflineData = {
        encrypted: encryptedData,
        userId: this.currentUserId,
        consentTimestamp: Date.now()
      };

      localStorage.setItem(STORAGE_KEYS.OFFLINE_DATA, JSON.stringify(secureData));
    } catch (error) {
      console.error('[OfflineStorage] Error saving encrypted offline data:', error);
      throw new Error('Kunne ikke lagre offline data');
    }
  }

  // Add project to offline storage
  async addOfflineProject(project: Omit<OfflineProject, 'isOffline' | 'syncStatus'>): Promise<void> {
    const offlineData = await this.getOfflineData();
    const offlineProject: OfflineProject = {
      ...project,
      isOffline: true,
      syncStatus: 'pending'
    };

    offlineData.projects.push(offlineProject);
    await this.saveOfflineData(offlineData);

    // Add to sync queue
    await this.addToSyncQueue('create-project', offlineProject);
  }

  // Cache online project for offline access
  async cacheOnlineProject(project: any): Promise<void> {
    console.log('[OfflineStorage] Caching project:', project.name || project.title, 'ID:', project._id || project.id);

    const offlineData = await this.getOfflineData();
    console.log('[OfflineStorage] Current offline data has', offlineData.projects.length, 'projects');

    // Check if project is already cached
    const projectId = project._id || project.id;
    const existingIndex = offlineData.projects.findIndex(p =>
      (p._id || p.id) === projectId
    );

    const cachedProject: OfflineProject = {
      ...project,
      isOffline: false, // Mark as cached online project
      syncStatus: 'cached',
      cachedAt: new Date().toISOString()
    };

    if (existingIndex >= 0) {
      console.log('[OfflineStorage] Updating existing cached project at index', existingIndex);
      offlineData.projects[existingIndex] = cachedProject;
    } else {
      console.log('[OfflineStorage] Adding new cached project');
      offlineData.projects.push(cachedProject);
    }

    await this.saveOfflineData(offlineData);
    console.log('[OfflineStorage] ✅ Successfully cached project:', project.name || project.title);
  }

  // Add project log to offline storage
  async addOfflineProjectLog(projectLog: Omit<OfflineProjectLog, 'isOffline' | 'syncStatus'>): Promise<void> {
    const offlineData = await this.getOfflineData();
    const offlineLog: OfflineProjectLog = {
      ...projectLog,
      isOffline: true,
      syncStatus: 'pending'
    };

    offlineData.projectLogs.push(offlineLog);
    await this.saveOfflineData(offlineData);

    // Add to sync queue
    await this.addToSyncQueue('create-project-log', offlineLog);
  }

  // Get projects (including offline ones)
  async getAllProjects(): Promise<OfflineProject[]> {
    const offlineData = await this.getOfflineData();
    return offlineData.projects;
  }

  // Get project logs for a project (including offline ones)
  async getProjectLogs(projectId: string): Promise<OfflineProjectLog[]> {
    const offlineData = await this.getOfflineData();
    return offlineData.projectLogs.filter(log => log.projectId === projectId);
  }

  // Add item to sync queue (encrypted)
  private async addToSyncQueue(type: string, data: any): Promise<void> {
    const queueItem = {
      type,
      data,
      timestamp: new Date().toISOString()
    };

    this.syncQueue.push(queueItem);
    await this.saveSyncQueue();
  }

  // Load sync queue from storage (encrypted)
  private async loadSyncQueue(): Promise<void> {
    if (!this.currentUserId || !offlineEncryption.isInitialized()) {
      this.syncQueue = [];
      return;
    }

    try {
      const encryptedQueueStr = localStorage.getItem(STORAGE_KEYS.SYNC_QUEUE);
      if (!encryptedQueueStr) {
        this.syncQueue = [];
        return;
      }

      const secureQueue: SecureOfflineData = JSON.parse(encryptedQueueStr);

      // Verify queue belongs to current user
      if (secureQueue.userId !== this.currentUserId) {
        this.syncQueue = [];
        return;
      }

      this.syncQueue = await offlineEncryption.decryptData(secureQueue.encrypted);
    } catch (error) {
      console.error('[OfflineStorage] Error loading encrypted sync queue:', error);
      this.syncQueue = [];
    }
  }

  // Save sync queue to storage (encrypted)
  private async saveSyncQueue(): Promise<void> {
    if (!this.currentUserId || !offlineEncryption.isInitialized()) {
      return;
    }

    try {
      const encryptedQueue = await offlineEncryption.encryptData(this.syncQueue);
      const secureQueue: SecureOfflineData = {
        encrypted: encryptedQueue,
        userId: this.currentUserId,
        consentTimestamp: Date.now()
      };

      localStorage.setItem(STORAGE_KEYS.SYNC_QUEUE, JSON.stringify(secureQueue));
    } catch (error) {
      console.error('[OfflineStorage] Error saving encrypted sync queue:', error);
    }
  }

  // Get sync queue
  getSyncQueue(): Array<{ type: string; data: any; timestamp: string }> {
    return [...this.syncQueue];
  }

  // Clear sync queue
  clearSyncQueue(): void {
    this.syncQueue = [];
    this.saveSyncQueue();
  }

  // Remove item from sync queue
  removeFromSyncQueue(index: number): void {
    this.syncQueue.splice(index, 1);
    this.saveSyncQueue();
  }

  // Update sync status
  updateSyncStatus(id: string, type: 'project' | 'projectLog', status: OfflineProject['syncStatus']): void {
    const offlineData = this.getOfflineData();
    
    if (type === 'project') {
      const project = offlineData.projects.find(p => p.id === id);
      if (project) {
        project.syncStatus = status;
        if (status === 'synced') {
          project.isOffline = false;
        }
      }
    } else {
      const log = offlineData.projectLogs.find(l => l.id === id);
      if (log) {
        log.syncStatus = status;
        if (status === 'synced') {
          log.isOffline = false;
        }
      }
    }
    
    this.saveOfflineData(offlineData);
  }

  /**
   * Migrate legacy unencrypted data to encrypted format
   */
  private async migrateLegacyData(): Promise<void> {
    try {
      // Check for legacy unencrypted data
      const legacyData = localStorage.getItem(STORAGE_KEYS.LEGACY_DATA);
      const legacyQueue = localStorage.getItem(STORAGE_KEYS.LEGACY_QUEUE);

      if (legacyData) {
        console.log('[OfflineStorage] Migrating legacy offline data to encrypted format');
        const oldData: OfflineData = JSON.parse(legacyData);
        await this.saveOfflineData(oldData);
        localStorage.removeItem(STORAGE_KEYS.LEGACY_DATA);
      }

      if (legacyQueue) {
        console.log('[OfflineStorage] Migrating legacy sync queue to encrypted format');
        this.syncQueue = JSON.parse(legacyQueue);
        await this.saveSyncQueue();
        localStorage.removeItem(STORAGE_KEYS.LEGACY_QUEUE);
      }
    } catch (error) {
      console.error('[OfflineStorage] Error migrating legacy data:', error);
    }
  }

  /**
   * Clear all offline data (GDPR compliant)
   */
  async clearOfflineData(): Promise<void> {
    // Clear encrypted data
    localStorage.removeItem(STORAGE_KEYS.OFFLINE_DATA);
    localStorage.removeItem(STORAGE_KEYS.SYNC_QUEUE);
    localStorage.removeItem(STORAGE_KEYS.LAST_SYNC);

    // Clear legacy data
    localStorage.removeItem(STORAGE_KEYS.LEGACY_DATA);
    localStorage.removeItem(STORAGE_KEYS.LEGACY_QUEUE);

    // Clear encryption keys
    offlineEncryption.clearEncryption();

    // Reset state
    this.syncQueue = [];
    this.currentUserId = null;

    console.log('[OfflineStorage] All offline data cleared');
  }

  /**
   * Logout cleanup - clear all user data
   */
  async logoutCleanup(): Promise<void> {
    await this.clearOfflineData();
    gdprCompliance.revokeConsent();
  }

  /**
   * Get storage usage information
   */
  getStorageUsage(): { used: number; available: number; percentage: number } {
    try {
      // Calculate used storage from localStorage
      let usedBytes = 0;
      for (let key in localStorage) {
        if (key.startsWith('jobblogg-')) {
          usedBytes += localStorage[key].length * 2; // UTF-16 encoding
        }
      }

      // Estimate available storage (5MB typical localStorage limit)
      const availableBytes = 5 * 1024 * 1024; // 5MB
      const percentage = (usedBytes / availableBytes) * 100;

      return {
        used: usedBytes,
        available: availableBytes,
        percentage: Math.min(percentage, 100)
      };
    } catch (error) {
      console.error('[OfflineStorage] Error calculating storage usage:', error);
      return { used: 0, available: 0, percentage: 0 };
    }
  }



  // Check if storage is nearly full
  isStorageNearlyFull(): boolean {
    const usage = this.getStorageUsage();
    return usage.percentage > 80; // Alert when over 80% full
  }
}

// Export singleton instance
export const offlineStorage = OfflineStorageManager.getInstance();

// Utility functions
export const OfflineUtils = {
  // Check if app is offline
  isOffline: (): boolean => !navigator.onLine,

  // Generate offline ID
  generateOfflineId: (): string => `offline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,

  // Format sync status for display
  formatSyncStatus: (status: OfflineProject['syncStatus']): string => {
    switch (status) {
      case 'pending':
        return 'Venter på synkronisering';
      case 'syncing':
        return 'Synkroniserer...';
      case 'synced':
        return 'Synkronisert';
      case 'error':
        return 'Synkroniseringsfeil';
      default:
        return 'Ukjent status';
    }
  },

  // Get sync status color
  getSyncStatusColor: (status: OfflineProject['syncStatus']): string => {
    switch (status) {
      case 'pending':
        return 'text-jobblogg-warning';
      case 'syncing':
        return 'text-jobblogg-primary';
      case 'synced':
        return 'text-jobblogg-success';
      case 'error':
        return 'text-jobblogg-error';
      default:
        return 'text-jobblogg-text-muted';
    }
  }
};
